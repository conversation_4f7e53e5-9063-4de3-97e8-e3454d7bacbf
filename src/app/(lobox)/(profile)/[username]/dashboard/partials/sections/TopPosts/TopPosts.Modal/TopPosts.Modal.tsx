import React from 'react';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalDialog from 'shared/uikit/Modal/ModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from 'shared/utils/hooks/useTranslation';
import TopPostsShowAll from '../TopPosts.ShowAll';
import classes from './TopPosts.Modal.module.scss';
import type { TopPostItemType } from '../index';
import type { ModalDialogProps } from 'shared/components/molecules';

type OmitChildrenModalDialogProps = Omit<ModalDialogProps, 'children'>;

export interface TopPostsModalProps extends OmitChildrenModalDialogProps {
  selectedPost?: TopPostItemType;
}

const TopPostsModal = ({
  isOpen,
  onClose,
}: TopPostsModalProps): JSX.Element => {
  const { t } = useTranslation();

  return (
    <ModalDialog isOpen={isOpen} onBack={onClose} onClose={onClose}>
      <ModalHeaderSimple title={t('top_posts')} visibleHeaderDivider />
      <ModalBody className={classes.modalBodyView}>
        <TopPostsShowAll />
      </ModalBody>
    </ModalDialog>
  );
};

export default TopPostsModal;
