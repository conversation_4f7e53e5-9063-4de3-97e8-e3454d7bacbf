import Flex from '@shared/uikit/Flex';

import useTranslation from '@shared/utils/hooks/useTranslation';
import Button from '@shared/uikit/Button';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';

interface BulkStageMenuProps {
  selectedCandidates: { id: string }[];
}

const BulkStageMenu: React.FC<BulkStageMenuProps> = (props) => {
  const { selectedCandidates } = props;
  const { t } = useTranslation();

  const onOpenBulkAction = () => {
    openMultiStepForm({
      formName: 'pipelineBulkAction',
      data: {
        step: '1',
        participationIds: selectedCandidates,
      },
    });
  };

  return (
    <Flex className="sticky bottom-0 left-0 w-full bg-popOverBg_white p-20 flex items-center border-t border-t-techGray_20 border-solid">
      <Button
        schema="semi-transparent"
        variant="default"
        className="!w-full"
        label={t('set_bulk_actions')}
        onClick={onOpenBulkAction}
        disabled={!selectedCandidates?.length}
      />
    </Flex>
  );
};

export default BulkStageMenu;
