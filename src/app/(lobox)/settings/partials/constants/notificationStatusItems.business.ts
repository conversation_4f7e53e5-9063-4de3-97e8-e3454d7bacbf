import NotificationsTypes from 'shared/utils/constants/notifications-types';
import routeKeysVariants from './routeKeysVariants';

const businessNotificationStatusItems = {
  [routeKeysVariants.generalNotifications]: {
    id: 'GENERAL',
    rootTitle: 'allow_general_notifications',
    items: [
      {
        title: 'avatar_notification',
        types: [NotificationsTypes.GENERAL.AVATAR],
      },
      {
        title: 'cover_notification',
        types: [NotificationsTypes.GENERAL.COVER],
      },
      {
        title: 'profile_completion_notification',
        types: [NotificationsTypes.GENERAL.PROFILE_COMPLETION],
      },
      {
        title: 'plan_subscription',
        types: [
          NotificationsTypes.GENERAL.PLAN_ACTIVATED,
          NotificationsTypes.GENERAL.PLAN_DOWNGRADED,
        ],
      },
      {
        title: 'recurring_payment',
        types: [NotificationsTypes.GENERAL.PLAN_RENEWED],
      },
      {
        title: 'cancel_subscription',
        types: [NotificationsTypes.GENERAL.PLAN_CANCELED],
      },
      // {
      //   title: 'new_member',
      //   types: [NotificationsTypes.GENERAL.PLAN_CANCELED],
      // },
    ],
  },
  [routeKeysVariants.jobNotifications]: {
    id: 'JOB',
    rootTitle: 'allow_job_notifications',
    items: [
      {
        title: 'job_candidates_notification',
        types: [NotificationsTypes.JOB.CANDIDATES],
      },
      {
        title: 'job_application_notification',
        types: [NotificationsTypes.JOB.JOB_APPLICATION],
      },
    ],
  },
  [routeKeysVariants.searchNotifications]: {
    id: 'SEARCH',
    rootTitle: 'allow_search_notifications',
    items: [
      {
        title: 'profile_show_up_notification',
        types: [NotificationsTypes.SEARCH.PROFILE_SHOW_UP],
      },
    ],
  },
  [routeKeysVariants.pageNotifications]: {
    id: 'PAGE',
    rootTitle: 'allow_page_notifications',
    items: [
      {
        title: 'premium_plan_notification',
        types: [NotificationsTypes.PAGE.PREMIUM_PLAN],
      },
      {
        title: 'update_and_engagement_notification',
        types: [NotificationsTypes.PAGE.UPDATE_AND_ENGAGEMENT],
      },
      {
        title: 'performance_and_insights_notification',
        types: [NotificationsTypes.PAGE.PERFORMANCE_AND_INSIGHTS],
      },
      {
        title: 'engagement_tips_notification',
        types: [NotificationsTypes.PAGE.ENGAGEMENT_TIPS],
      },
      {
        title: 'promotional_tools_notification',
        types: [NotificationsTypes.PAGE.PROMOTIONAL_TOOLS],
      },
      {
        title: 'event_promotion_notification',
        types: [NotificationsTypes.PAGE.EVENT_PROMOTION],
      },
      {
        title: 'milestone_achievement_notification',
        types: [NotificationsTypes.PAGE.MILESTONE_ACHIEVEMENT],
      },
    ],
  },
  [routeKeysVariants.clubNotifications]: {
    id: 'CLUB',
    rootTitle: 'allow_club_notifications',
    items: [
      {
        title: 'club_permission_notification',
        types: [NotificationsTypes.CLUB.CLUB_PERMISSION],
      },
      {
        title: 'post_publishment_notification',
        types: [NotificationsTypes.CLUB.POST_PUBLISHMENT],
      },
      {
        title: 'club_invitation_notification',
        types: [NotificationsTypes.CLUB.CLUB_INVITATION],
      },
      {
        title: 'following_post_share_notification',
        types: [NotificationsTypes.CLUB.FOLLOWING_POST_SHARE],
      },
    ],
  },
};

export default businessNotificationStatusItems;
