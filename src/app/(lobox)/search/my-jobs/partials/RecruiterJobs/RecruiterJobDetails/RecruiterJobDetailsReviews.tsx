// TODO refactor this file to use a normal List component instead of a SearchList;

import ReviewItem from '@shared/components/molecules/ReviewItem';
import { type FC } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { QueryKeys } from 'shared/utils/constants';
import SearchList from 'shared/components/Organism/SearchList';
import EmptySearchResult from 'shared/components/Organism/EmptySearchResult';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import { getReviewsOfJob } from '@shared/utils/api/jobs';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import classes from './RecruiterJobDetailsStyles.module.scss';

interface RecruiterJobDetailsReviewsProps {
  jobId: string;
}

const RecruiterJobDetailsReviews: FC<RecruiterJobDetailsReviewsProps> = ({
  jobId,
}) => {
  const { t } = useTranslation();
  const { content, totalElements, totalPages, setPage, page, isLoading } =
    usePaginateQuery({
      action: {
        apiFunc: (props) =>
          getReviewsOfJob({
            ...props.params,
            size: 10,
            jobId,
          }),
        key: [QueryKeys.jobReviews, jobId],
      },
    });

  return (
    <SearchList
      entity="recruiterJobs"
      isLoading={isLoading}
      totalElements={Number(totalElements)}
      data={content}
      onPageChange={setPage}
      totalPages={Number(totalPages)}
      className={{ root: classes.applicantsRoot }}
      renderItem={(review) => (
        <ReviewItem
          item={review}
          variant="candidate"
          key={`review_${review.id}`}
        />
      )}
      emptyList={
        <EmptySearchResult
          title={t('no_review')}
          sectionMessage={t('no_review_found_desc_for_job')}
          className={classes.emptyResult}
          classNames={{ description: '!mt-12' }}
        />
      }
      parentPage={page}
      noItemButtonAction
      innerList
    />
  );
};

export default RecruiterJobDetailsReviews;
