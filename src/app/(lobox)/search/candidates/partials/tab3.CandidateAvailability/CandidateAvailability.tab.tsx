import useTranslation from '@shared/utils/hooks/useTranslation';
import { useState, type FC } from 'react';
import AvailabilityWeek from '@shared/components/Organism/AvailabilityCalendars/AvailabilityWeek';
import Flex from '@shared/uikit/Flex/index';
import Button from '@shared/uikit/Button';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import SchedulesCalendarTabbedLayout from '@shared/components/layouts/SchedulesCalendarTabbedLayout/SchedulesCalendarTabbedLayout';
import AvailabilityDay from '@shared/components/Organism/AvailabilityCalendars/AvailabilityDay';

import { BaseCandidateSectionProp } from '@shared/types/candidates';

export const CandidateAvailabilityTab: FC<BaseCandidateSectionProp> = (
  props
) => {
  const { t } = useTranslation();
  const [availabilityState, setAvailabilityState] = useState('week');

  const renderTab = (
    <Flex flexDir="row" className="mx-[20px] my-12 gap-8">
      <Button
        label={t('day')}
        schema={
          availabilityState === 'day' ? 'semi-transparent' : 'transparent'
        }
        className="hover:bg-techGray_20"
        onClick={() => setAvailabilityState('day')}
      />
      <Button
        label={t('week')}
        schema={
          availabilityState === 'week' ? 'semi-transparent' : 'transparent'
        }
        className="hover:bg-techGray_20"
        onClick={() => setAvailabilityState('week')}
      />
    </Flex>
  );

  if (!props?.candidate?.isLoboxUser) {
    return (
      <EmptySearchResult
        title={t('no_availability_title')}
        sectionMessage={t('no_availability_message')}
      />
    );
  }

  return (
    <SchedulesCalendarTabbedLayout renderTab={renderTab}>
      {availabilityState === 'week' && (
        <AvailabilityWeek
          userId={props?.candidate?.profile?.originalId}
          hasOpenModal={false}
        />
      )}
      {availabilityState === 'day' && (
        <AvailabilityDay
          userId={props?.candidate?.profile?.originalId}
          hasOpenModal={false}
        />
      )}
    </SchedulesCalendarTabbedLayout>
  );
};
