import React from 'react';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import BaseButton from '@shared/uikit/Button/BaseButton';
import IconButton from '@shared/uikit/Button/IconButton';
import CheckBox from '@shared/uikit/CheckBox';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import Switch from '@shared/uikit/Switch';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import classes from './CandidatesList.module.scss';

interface Candidate {
  id: string;
}

interface HeaderElementProps {
  isLoadingAPis: boolean;
  isBulk: boolean;
  setIsBulk: (value: boolean | ((prev: boolean) => boolean)) => void;
  totalElements: number;
  onlyApplicants: boolean;
  handleChangeParams: (params: {
    add?: { onlyApplicants: string } | undefined;
    remove?: string[] | undefined;
  }) => void;
  checkedIds: string[] | null;
  candidates: Candidate[] | null;
  setCheckIds: (ids: string[]) => void;
}

const CandidatesHeaderElement = ({
  isLoadingAPis,
  isBulk,
  setIsBulk,
  totalElements,
  onlyApplicants,
  handleChangeParams,
  checkedIds,
  candidates,
  setCheckIds,
}: HeaderElementProps): React.ReactElement => {
  const { t } = useTranslation();

  if (isLoadingAPis) {
    return <Skeleton className="rounded !w-[120px] !h-[22px] !mt-[9px]" />;
  }

  return (
    <Flex className="w-full">
      <Flex flexDir="row" className="justify-between w-full items-center">
        <Flex flexDir="row" className="gap-12 items-center">
          <IconButton
            color="brand"
            name={isBulk ? 'bulk-intermediate-s' : 'bulk'}
            onClick={() => setIsBulk((prev) => !prev)}
            variant="rectangle"
            colorSchema={isBulk ? 'semi-transparent' : 'smoke_coal'}
            size="md18"
            type="far"
          />
          <Typography size={14} fontWeight={400} color="secondaryDisabledText">
            {totalElements} {t('lower_results')}
          </Typography>
        </Flex>
        <Flex>
          {!isBulk ? (
            <Switch
              value={onlyApplicants}
              onChange={() => {
                handleChangeParams({
                  add: !onlyApplicants ? { onlyApplicants: 'true' } : undefined,
                  remove: onlyApplicants ? ['onlyApplicants'] : undefined,
                });
              }}
              className={classes.headerSwitch}
              label={t('applicants')}
              labelProps={{
                color: 'secondaryDisabledText',
                font: '400',
              }}
            />
          ) : (
            checkedIds?.length && (
              <BaseButton
                onClick={() => {
                  openMultiStepForm({
                    formName: 'candidateBulkAction',
                    data: {
                      step: '1',
                      candidateIds: checkedIds,
                    },
                  });
                }}
              >
                <Typography fontSize={15} fontWeight={700} color="brand">
                  {t('set_bulk_actions')}
                </Typography>
              </BaseButton>
            )
          )}
        </Flex>
      </Flex>
      {isBulk && (
        <Flex
          flexDir="row"
          className="pt-16 mt-16 !border-t !border-solid !border-t-techGray_10 items-center justify-between"
        >
          <Typography fontSize={14} fontWeight={400} color="primaryText">
            {translateReplacer(t('num_selected_canidates'), [
              checkedIds?.length?.toString() || '0',
              candidates?.length?.toString() || '0',
            ])}
          </Typography>

          <Flex flexDir="row" className="gap-10">
            <Typography fontSize={14} fontWeight={400} color="primaryText">
              {t('select_the_first')} {candidates?.length}{' '}
            </Typography>

            <CheckBox
              value={checkedIds?.length === candidates?.length}
              onChange={(checked: boolean) => {
                setCheckIds(
                  checked ? candidates?.map((item) => item.id) || [] : []
                );
              }}
            />
          </Flex>
        </Flex>
      )}
    </Flex>
  );
};

export default CandidatesHeaderElement;
