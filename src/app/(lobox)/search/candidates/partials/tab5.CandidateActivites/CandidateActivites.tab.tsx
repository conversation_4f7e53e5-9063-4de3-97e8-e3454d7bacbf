import { type FC } from 'react';
import ActivityItem from '@shared/components/molecules/ActivityItem';
import ActivityItemSkeleton from '@shared/components/molecules/ActivityItem/ActivityItemSkeleton';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import Section from '@shared/components/molecules/Section';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import { searchCandidateActivities } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import { useDebounceState } from '@shared/utils/hooks/useDebounceState';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { ActivityProps } from '@shared/types/activityProps';
import type { BaseCandidateSectionProp } from '@shared/types/candidates';

export const CandidateActivitesTab: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const keyword = useDebounceState('', 500);

  const infiniteQuery = useReactInfiniteQuery<ActivityProps>(
    [QueryKeys.searchCandidateActivities, candidate.id, keyword.debounceValue],
    {
      func: searchCandidateActivities,
      size: 20,
      extraProps: {
        candidateId: candidate.id,
        text: keyword.debounceValue,
      },
    },
    {
      enabled: !!candidate.id,
      refetchOnWindowFocus: false,
    }
  );

  const activities = (infiniteQuery.data ?? []).map((act) => ({
    ...act,
    user: act.user ?? {},
  }));

  return (
    <Section
      title={t('activities')}
      hasSearchInput
      onSearchChanged={keyword.setValue}
      searchInputProps={{ size: undefined }}
      className="!flex-1"
    >
      {infiniteQuery.isLoading ? (
        <CardWrapper>
          <ActivityItemSkeleton />
          <ActivityItemSkeleton />
          <ActivityItemSkeleton />
        </CardWrapper>
      ) : activities.length > 0 ? (
        <CardWrapper>
          {activities.map((activity) => (
            <ActivityItem key={activity.id} item={activity} />
          ))}
        </CardWrapper>
      ) : (
        <EmptySearchResult
          title={t('no_activities')}
          sectionMessage={t('no_activities_for_candidate_desc')}
          className="!py-32 px-0 !m-0"
        />
      )}
    </Section>
  );
};
