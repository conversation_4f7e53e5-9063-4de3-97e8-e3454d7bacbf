'use client';

import dynamic from 'next/dynamic';
import React, { useCallback, useEffect, useState } from 'react';
import SearchListWithDetailsLayout from '@shared/components/layouts/SearchListWithDetailsLayout';
import CandidateListItem from '@shared/components/molecules/CandidateCard/CandidateListItem';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import CandidateSavedSearchFilters from '@shared/components/Organism/CandidateSavedSearchFilters';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import EmptyState from '@shared/components/Organism/EmptyState/EmptyState';
import { CandidateFormStepKeys } from '@shared/components/Organism/MultiStepForm/CreateCandidateForm/constants';
import PlanRestrictionCard from '@shared/components/Organism/PlanRestrictionCard';
import SearchFiltersModalSkeleton from '@shared/components/Organism/SearchFiltersModal/SearchFiltersModal.skeleton';
import SearchList from '@shared/components/Organism/SearchList';
import {
  useSearchDispatch,
  useSearchState,
} from '@shared/contexts/search/search.provider';
import { useCandidatesFilterGroups } from '@shared/hooks/searchFilters/useCandidatesFilterGroups';
import useSearchResultWithFilters from '@shared/hooks/searchFilters/useSearchResultWithFilters';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import { openNineDotPanel } from '@shared/stores/nineDotPanelStore';
import { FeatureName } from '@shared/types/planRestriction';
import Button from '@shared/uikit/Button';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import useTranslation from '@shared/utils/hooks/useTranslation';
import CandidateDetails from './partials/CandidateDetails.component';
import CandidatesHeaderElement from './partials/CandidatesHeaderElement';
import classes from './styles.module.scss';
import type { JSX } from 'react';

const SaveSearchFiltersModal = dynamic(
  () => import('@shared/components/Organism/SaveSearchFiltersModal'),
  { ssr: false, loading: () => <SearchFiltersModalSkeleton /> }
);

const CandidatesPage = (): JSX.Element => {
  const { t } = useTranslation();
  const searchDispatch = useSearchDispatch();
  const isSaveSearchFilterModalOpen = useSearchState(
    'saveSearchFilterModalData'
  )?.isOpen;

  const { handleChangeParams, allParams } = useCustomParams();
  const { currentEntityId } = allParams;
  const onlyApplicants = allParams.onlyApplicants === 'true';
  const isOpenPlans = allParams.openPlans === 'yes';
  const visibleSavedFilters = allParams.candidateSearchType === 'saved';
  const groups = useCandidatesFilterGroups();
  const [isBulk, setIsBulk] = useState(false);
  const [checkedIds, setCheckIds] = useState<string[]>([]);
  const isSavedCandidate = allParams?.candidateSearchType === 'saved_candidate';

  const {
    totalElements,
    totalPages,
    content: candidates = [],
    isEmpty,
    isLoading: isFetching,
    setPage,
    refetch,
    isFetchingFilters,
  } = useSearchResultWithFilters({
    entity: 'candidates',
    extraParams: {
      onlySaved: isSavedCandidate,
    },
  });

  useEffect(() => {
    if (isOpenPlans)
      openNineDotPanel({
        isOpen: true,
        defaultActiveStep: 'PLANS',
      });
  }, [isOpenPlans]);

  const toggleSaveFilterModal = (isOpen: boolean) => {
    searchDispatch({
      type: 'SET_SAVE_SEARCH_FILTER_MODAL_DATA',
      payload: {
        isOpen,
      },
    });
  };

  const closeSaveFilterHandler = () => toggleSaveFilterModal(false);

  const handleOpenCreateModal = useCallback(() => {
    openMultiStepForm({
      formName: 'createCandidateForm',
      stepKey: CandidateFormStepKeys.NEW,
    });
  }, []);

  const onChangeChecked = ({
    checked,
    id,
  }: {
    id: string;
    checked: boolean;
  }) => {
    setCheckIds((prev) => {
      if (checked) {
        return prev.includes(id) ? prev : [...prev, id];
      }

      return prev.filter((item) => item !== id);
    });
  };

  return (
    <>
      <SearchListWithDetailsLayout
        isFullWidth
        groups={groups}
        isLoading={isFetchingFilters}
        isTotalyEmpty={Boolean(isEmpty && !isFetching)}
        classNames={{ emptyState: '!max-w-none' }}
        filterTopComponent={
          visibleSavedFilters ? (
            <CandidateSavedSearchFilters
              isLoading={isFetching}
              groups={groups}
            />
          ) : undefined
        }
        headerHeight={visibleSavedFilters ? 120 : undefined}
        sectionTotalEmpty={
          <CardWrapper
            classNames={{
              root: '!my-20 !h-full',
              container: '!h-full justify-center items-center',
            }}
          >
            <EmptySearchResult title={t('no_content_found')} />
          </CardWrapper>
        }
        headerComponents={
          <>
            <DividerVertical />
            <Button
              label={t('create')}
              leftIcon="plus"
              disabled={isFetching}
              onClick={() => handleOpenCreateModal()}
            />
          </>
        }
        listTopComponent={
          <PlanRestrictionCard
            className="mt-20"
            featuresName={[FeatureName.CANDIDATE_SEARCH_LIMITATION]}
          />
        }
        listComponent={
          <SearchList
            entity="candidates"
            isLoading={isFetching}
            title=" "
            showCustomComponent
            data={candidates}
            emptyList={
              <EmptyState
                message={
                  onlyApplicants
                    ? t('no_applicants_found')
                    : t('no_candidates_found')
                }
                caption=""
                className="h-full flex justify-center items-center"
              />
            }
            totalPages={totalPages}
            onPageChange={setPage}
            scrollToTopWhenClick
            noItemButtonAction
            headerElement={
              <CandidatesHeaderElement
                isLoadingAPis={isFetching}
                isBulk={isBulk}
                setIsBulk={setIsBulk}
                totalElements={totalElements}
                onlyApplicants={onlyApplicants}
                handleChangeParams={handleChangeParams}
                checkedIds={checkedIds}
                candidates={candidates}
                setCheckIds={setCheckIds}
              />
            }
            renderItem={(item) => (
              <CandidateListItem
                item={item as any}
                key={item.id}
                refetch={refetch}
                hasCheckBox={isBulk}
                setCheckedIds={onChangeChecked}
                checkedIds={checkedIds}
                isFocused={item.id === currentEntityId}
                className={classes.listItem}
                onClick={() => {
                  handleChangeParams({ add: { currentEntityId: item.id } });
                }}
              />
            )}
          />
        }
        detailsComponent={
          <CandidateDetails
            key={currentEntityId}
            candidateId={currentEntityId}
            parentLoading={isFetching || isFetchingFilters}
          />
        }
        hasBackBtn={false}
      />
      {isSaveSearchFilterModalOpen && (
        <SaveSearchFiltersModal
          onClose={closeSaveFilterHandler}
          groups={[
            {
              name: 'text',
              cp: 'input',
              label: t('searched_txt'),
              helperText: t('this_is_tsym_b_tsf'),
              wrapStyle: 'col-span-full formItemFullWidthFirst',
              visibleOptionalLabel: false,
            },
            ...groups,
          ]}
        />
      )}
    </>
  );
};

export default CandidatesPage;
