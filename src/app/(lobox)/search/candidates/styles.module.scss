@import '/src/shared/theme/theme.scss';

.listItem {
  margin-bottom: variables(xLargeGutter) * 0.5;
  position: relative;
}

.headerSwitch {
  gap: variables(gutter) * 0.5;
}

.headerRoot {
  max-width: unset;
}
.resultHeader {
  position: sticky;
  top: 0;
  background-color: colors(background2);
  z-index: 2;
}

.contentRoot {
  overflow-y: hidden;
  max-width: unset;
  .list {
    max-width: 500px;
  }
  .scrollbarFix {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
    & > div {
      flex: unset !important;
    }
  }
}

.filtersSkeleton {
  height: 56px;
  padding: 0 variables(largeGutter);
  background-color: colors(background);
  flex-direction: row;
  gap: variables(gutter) * 0.5;
  align-items: center;
  > * {
    height: 32px;
    width: 110px;
    border-radius: 999px;
    &:first-child {
      border-radius: 4px;
    }
    &:last-child {
      margin-left: auto;
      border-radius: 4px;
    }
  }
}

.listSkeleton {
  gap: variables(xLargeGutter) * 0.5;
  margin-top: variables(largeGutter);
}

.listHeaderSkeleton {
  border-radius: 4px;
  padding: variables(xLargeGutter) * 0.5 variables(largeGutter);
  border-radius: variables(largeGutter) * 0.5;
  background: colors(background);
  gap: 4px;
}
.skeletonTitle {
  border-radius: 4px;
  width: 150px;
  height: 19px;
}
.skeletonTitle2 {
  border-radius: 4px;
  width: 120px;
  height: 16px;
}
