// TODO refactor this file to use a normal List component instead of a SearchList;

import { QueryKeys } from 'shared/utils/constants';
import { type FC } from 'react';
import SearchList from 'shared/components/Organism/SearchList';
import EmptySearchResult from 'shared/components/Organism/EmptySearchResult';
import useTranslation from 'shared/utils/hooks/useTranslation';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import { getProjectEntitiesAPI } from '@shared/utils/api/project';
import type { JobAPIProps } from '@shared/types/jobsProps';
import Button from '@shared/uikit/Button';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import BusinessJobCardInList from '@shared/components/molecules/BusinessJobCard/BusinessJobCardInList';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import BusinessJobCardActions from '@shared/components/molecules/BusinessJobCard/BusinessJobCardActions';
import HorizontalTagList from '@shared/components/molecules/HorizontalTagList';
import classes from './RecruiterProjectDetailsStyles.module.scss';
import { PROJECT_ENTITIES_VARIANT } from '@shared/constants/projects';

interface RecruiterProjectDetailsJobsProps {
  projectId: string;
}

const RecruiterProjectDetailsJobs: FC<RecruiterProjectDetailsJobsProps> = ({
  projectId,
}) => {
  const { t } = useTranslation();
  const { content, totalElements, totalPages, page, setPage, isLoading } =
    usePaginateQuery<JobAPIProps>({
      action: {
        apiFunc: getProjectEntitiesAPI,
        key: [QueryKeys.projectJobsList, projectId],
        params: {
          variant: PROJECT_ENTITIES_VARIANT.jobs,
          projectId,
          size: 10,
        },
      },
    });

  const handleCreate = () => openMultiStepForm({ formName: 'createJobForm' });

  return (
    <SearchList
      entity="recruiterJobsInner"
      isLoading={isLoading}
      totalElements={Number(totalElements)}
      data={content}
      onPageChange={setPage}
      totalPages={Number(totalPages)}
      className={{ root: classes.sectionRoot }}
      noItemButtonAction
      renderItem={(job) => (
        <BusinessJobCardInList
          job={job}
          key={`job_${job.id}`}
          cardProps={{
            classNames: { root: classes.jobItem },
          }}
          hasRedirection
          actions={<BusinessJobCardActions job={job} />}
          tags={<HorizontalTagList tags={job.tags} />}
        />
      )}
      emptyList={
        <EmptySearchResult
          title={t('no_jobs')}
          sectionMessage={translateReplacer(t('no_antity_in_object'), [
            t('jobs').toLowerCase(),
            t('create_one').toLowerCase(),
          ])}
          className={classes.emptyList}
          classNames={{ description: '!mt-12' }}
        >
          <Button
            label={t('create')}
            className="mt-20"
            leftIcon="plus"
            leftType="far"
            onClick={handleCreate}
          />
        </EmptySearchResult>
      }
      parentPage={page}
      innerList
    />
  );
};

export default RecruiterProjectDetailsJobs;
