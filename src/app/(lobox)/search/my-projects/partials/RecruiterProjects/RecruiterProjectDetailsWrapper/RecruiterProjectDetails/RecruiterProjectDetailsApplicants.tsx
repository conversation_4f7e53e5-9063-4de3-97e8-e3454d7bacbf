// TODO refactor this file to use a normal List component instead of a SearchList;

import ApplicantCard from 'shared/components/molecules/ApplicantCard';
import EmptySearchResult from 'shared/components/Organism/EmptySearchResult';
import SearchList from 'shared/components/Organism/SearchList';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, type FC } from 'react';
import useChangePipeline from 'shared/hooks/useChangePipeline';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { QueryKeys } from 'shared/utils/constants';
import { getProjectEntitiesAPI } from '@shared/utils/api/project';
import type { JobParticipationModel } from '@shared/types/jobsProps';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import type { ProjectProps } from '@shared/types/project';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import { PROJECT_ENTITIES_VARIANT } from '@shared/constants/projects';
import classes from './RecruiterProjectDetailsStyles.module.scss';

interface RecruiterProjectDetailsApplicantsProps {
  project: ProjectProps;
}

const RecruiterProjectDetailsApplicants: FC<
  RecruiterProjectDetailsApplicantsProps
> = ({ project }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const projectId = project.id;
  const appDispatch = useGlobalDispatch();

  const { data, totalElements, totalPages, isLoading, refetch } =
    useReactInfiniteQuery<JobParticipationModel>(
      [QueryKeys.projectApplications, projectId],
      {
        func: getProjectEntitiesAPI,
        size: 10,
        extraProps: {
          variant: PROJECT_ENTITIES_VARIANT.applicants,
          projectId,
        },
      }
    );

  const { onChangePipeline } = useChangePipeline({
    onSuccess: () => {
      refetch();
      queryClient.refetchQueries({
        queryKey: [QueryKeys.projectActivities, projectId, 0],
      });
    },
    variant: 'applicant',
  });

  const handleOpenManagerModal = useCallback(
    (candidateId: string, tab: CandidateManagerTabkeys) => {
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          tab,
          id: candidateId,
          enableNavigate: false,
        },
      });
    },
    [appDispatch]
  );

  return (
    <SearchList
      entity="recruiterJobs"
      isLoading={isLoading}
      totalElements={Number(totalElements)}
      data={data ?? []}
      onPageChange={() => {}}
      totalPages={Number(totalPages)}
      className={{ root: classes.applicantsRoot }}
      noItemButtonAction
      renderItem={(application) => (
        <ApplicantCard
          data={application}
          onChangePipeline={(pipeline) =>
            onChangePipeline({
              userId: application.id,
              pipelineId: pipeline.id as string,
            })
          }
          key={`application_${application.id}`}
          showJob
          onPrimaryButtonClick={() => {
            handleOpenManagerModal(application.applicant.id, 'notes');
          }}
        />
      )}
      emptyList={
        <EmptySearchResult
          title={t('no_applicant')}
          sectionMessage={translateReplacer(t('no_antity_in_object'), [
            t('applicants').toLowerCase(),
            t('share_job').toLowerCase(),
          ])}
          className={classes.emptyList}
          classNames={{ description: '!mt-12' }}
        />
      }
      // parentPage={0}
      innerList
    />
  );
};

export default RecruiterProjectDetailsApplicants;
