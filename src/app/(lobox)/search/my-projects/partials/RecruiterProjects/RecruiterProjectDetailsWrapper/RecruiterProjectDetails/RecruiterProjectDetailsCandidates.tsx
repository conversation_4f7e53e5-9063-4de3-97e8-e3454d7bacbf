// TODO refactor this file to use a normal List component instead of a SearchList;

import ApplicantCard from 'shared/components/molecules/ApplicantCard';
import EmptySearchResult from 'shared/components/Organism/EmptySearchResult';
import SearchList from 'shared/components/Organism/SearchList';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, type FC } from 'react';
import useChangePipeline from 'shared/hooks/useChangePipeline';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { PROJECT_ENTITIES_VARIANT } from '@shared/constants/projects';
import { getProjectEntitiesAPI } from '@shared/utils/api/project';
import type { CandidateProps } from '@shared/types/jobsProps';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import Button from '@shared/uikit/Button';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import classes from './RecruiterProjectDetailsStyles.module.scss';
import { QueryKeys } from '@shared/utils/constants';

interface RecruiterProjectDetailsCandidatesProps {
  projectId: string;
}

const RecruiterProjectDetailsCandidates: FC<
  RecruiterProjectDetailsCandidatesProps
> = ({ projectId }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const appDispatch = useGlobalDispatch();

  const { data, totalElements, totalPages, isLoading, refetch } =
    useReactInfiniteQuery<CandidateProps>(
      [QueryKeys.projectCandidates, projectId],
      {
        func: (props) =>
          getProjectEntitiesAPI<CandidateProps>({
            projectId,
            variant: 'candidates',
            page: props.pageParam,
            onlyDone: false,
          }),
        size: 10,
        extraProps: {
          variant: PROJECT_ENTITIES_VARIANT.candidates,
          projectId,
        },
      }
    );

  const handleOpenManagerModal = useCallback(
    (candidateId: string, tab: CandidateManagerTabkeys) => {
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          tab,
          id: candidateId,
          enableNavigate: false,
        },
      });
    },
    [appDispatch]
  );

  const { onChangePipeline } = useChangePipeline({
    onSuccess: () => {
      refetch();
      queryClient.refetchQueries({
        queryKey: [QueryKeys.projectActivities, projectId, 0],
      });
    },
    variant: 'candidate',
  });

  return (
    <SearchList
      entity="recruiterJobs"
      isLoading={isLoading}
      totalElements={Number(totalElements)}
      data={data ?? []}
      onPageChange={() => {}}
      totalPages={Number(totalPages)}
      className={{ root: classes.applicantsRoot }}
      noItemButtonAction
      renderItem={(candidate) => (
        <ApplicantCard
          data={candidate}
          onChangePipeline={(pipeline) =>
            onChangePipeline({
              userId: candidate.id,
              pipelineId: pipeline.id as string,
            })
          }
          key={`candidate_${candidate.id}`}
          showJob
          variant="candidate"
          onPrimaryButtonClick={() => {
            handleOpenManagerModal(candidate.candidate.id, 'notes');
          }}
        />
      )}
      emptyList={
        <EmptySearchResult
          title={t('no_candidate')}
          sectionMessage={translateReplacer(t('no_antity_in_object'), [
            t('candidates').toLowerCase(),
            t('link_candidate').toLowerCase(),
          ])}
          className={classes.emptyList}
          classNames={{ description: '!mt-12' }}
        />
      }
      // parentPage={0}
      innerList
    />
  );
};

export default RecruiterProjectDetailsCandidates;
