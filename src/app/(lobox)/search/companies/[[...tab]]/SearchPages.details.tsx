import dynamic from 'next/dynamic';
import React, { useEffect, useState } from 'react';
import CompanyCardActions from '@app/search/companies/[[...tab]]/partials/components/CompanyCardActions';
import RightSearchResultSkeleton from '@app/search/partials/Skeletons/RightSearchResultSkeleton';
import CandidateCard from '@shared/components/molecules/CandidateCard';
import EmptyState from '@shared/components/Organism/EmptyState/EmptyState';
import Tabs from '@shared/components/Organism/Tabs';
import EmptySearchSvg from '@shared/svg/EmptySearchSvg';
import { CompanyClientVendorStatus, CompanyTab } from '@shared/types/company';
import { getCompanyDetailInVendorClientByPageId } from '@shared/utils/api/company';
import { db } from '@shared/utils/constants/enums';
import noCacheQueryClientOptions from '@shared/utils/constants/noCacheQueryClientOptions';
import QueryKeys from '@shared/utils/constants/queryKeys';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import collectionToObjectByKey from '@shared/utils/toolkit/collectionToObjectByKey';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { ClientVendorPendingBadge } from './partials/components/ClientVendorPendingBadge';
import CompanyActionButton from './partials/components/CompanyActionButton';
import CompanyCardBadges from './partials/components/CompanyCardBadges';
import CompanyDetailsTabSkeleton from './partials/components/CompanyDetailsTabs/CompanyDetails/CompanyDetailsTab.skeleton';
import classes from './SearchPages.details.module.scss';
import type { CompanyType, ECompanyRole } from '@shared/types/company';

const CompanyDetailsTab = dynamic(
  () => import('./partials/components/CompanyDetailsTabs/CompanyDetails'),
  { loading: () => <CompanyDetailsTabSkeleton /> }
);
const CompanyActivities = dynamic(
  () => import('./partials/components/CompanyDetailsTabs/CompanyActivities'),
  { loading: () => <CompanyDetailsTabSkeleton /> }
);

const pagesCategories = collectionToObjectByKey(db.CATEGORY_TYPES);

const tabs = [
  {
    path: 'details',
    title: 'details',
  },
  // {
  //   path: 'jobs',
  //   title: 'jobs',
  // },
  // {
  //   path: 'candidates',
  //   title: 'candidates',
  // },
  // {
  //   path: 'collaborators',
  //   title: 'collaborators',
  // },
  {
    path: 'activities',
    title: 'activities',
  },
  // {
  //   path: 'insights',
  //   title: 'insights',
  // },
];

type TabPath = (typeof tabs)[number]['path'];

interface SearchPagesDetailsProps {
  isLoading?: boolean;
  activeTab: CompanyTab;
  currentEntityId: string;
  params: Record<string, string>;
  myCompanyRole: ECompanyRole;
}

const SearchPagesDetails: React.FC<SearchPagesDetailsProps> = ({
  isLoading,
  activeTab,
  params,
  currentEntityId,
  myCompanyRole,
}) => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState('details');

  useEffect(() => {
    setSelectedTab('details');
  }, [currentEntityId]);

  const {
    isFetching,
    data: company,
    refetch,
  } = useReactQuery<CompanyType>({
    action: {
      key: [QueryKeys.companyDetail, params],
      apiFunc: getCompanyDetailInVendorClientByPageId,
      // spreadParams: true,
      params,
    },
    config: {
      ...noCacheQueryClientOptions,
      enabled: !!currentEntityId,
    },
  });
  const visibleActions = [CompanyTab.VENDORS, CompanyTab.CLIENTS].includes(
    activeTab
  );
  const visibleTabs = tabs.reduce(
    (acc, tab) => {
      if (
        ![company?.clientStatus, company?.vendorStatus].includes(
          CompanyClientVendorStatus.APPROVED
        ) &&
        tab.path === 'activities'
      ) {
        return acc;
      }

      return [...acc, { ...tab, title: t(tab.title) }];
    },
    [] as typeof tabs
  );

  if (isFetching || isLoading) {
    return <RightSearchResultSkeleton page />;
  }
  if (!company) {
    return (
      <EmptyState
        image={<EmptySearchSvg />}
        captionProps={{ color: 'smoke_coal', size: 20, font: '700' }}
        caption={translateReplacer(t('no_name_found'), [
          myCompanyRole.toLowerCase(),
        ])}
        message={translateReplacer(t('there_are_no_name_to_show'), [
          myCompanyRole.toLowerCase(),
        ])}
        className="h-full flex justify-center items-center"
      />
    );
  }

  return (
    <>
      <CandidateCard
        avatar={company.croppedImageUrl}
        firstText={company.title}
        secondText={company.usernameAtSign}
        thirdText={t(pagesCategories[company.category?.value]?.label)}
        fourthText={company.location?.label}
        isPage
        treeDotMenu={
          visibleActions ? (
            <CompanyCardActions activeTab={activeTab} company={company} />
          ) : undefined
        }
        footer={
          <Tabs
            activePath={selectedTab}
            onChangeTab={setSelectedTab}
            styles={{
              tabsRoot: classes.tabsRoot,
            }}
            tabs={visibleTabs}
          />
        }
        topRightActionComponent={
          activeTab === CompanyTab.REQUESTS ? (
            <ClientVendorPendingBadge />
          ) : undefined
        }
      >
        <CompanyActionButton
          company={company}
          role={myCompanyRole}
          activeTab={activeTab}
          refetchDetails={refetch}
          badges={
            activeTab === CompanyTab.CLIENTS ||
            activeTab === CompanyTab.VENDORS ? (
              <CompanyCardBadges
                jobCount={company.jobCount}
                candidateCount={company.candidateCount}
                collaboratorCount={company.collaboratorCount}
              />
            ) : undefined
          }
          currentEntityId={currentEntityId}
        />
      </CandidateCard>
      <Panels company={company} tab={selectedTab} />
    </>
  );
};

export default SearchPagesDetails;

const Panels = ({ tab, company }: { tab: TabPath; company: CompanyType }) => {
  switch (tab) {
    case 'activities': {
      return <CompanyActivities company={company} />;
    }
    default: {
      return <CompanyDetailsTab company={company} />;
    }
  }
};
