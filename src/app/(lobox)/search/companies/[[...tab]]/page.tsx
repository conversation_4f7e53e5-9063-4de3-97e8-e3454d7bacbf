'use client';

import React from 'react';
import { getMyCompanyRole } from '@app/search/companies/[[...tab]]/partials/utils/getMyCompanyRole';
import SearchListWithDetailsLayout from '@shared/components/layouts/SearchListWithDetailsLayout';
import EmptyState from '@shared/components/Organism/EmptyState/EmptyState';
import PlanRestrictionCard from '@shared/components/Organism/PlanRestrictionCard';
import SearchList from '@shared/components/Organism/SearchList';
import { useCompaniesFilterGroups } from '@shared/hooks/searchFilters/useCompaniesFilterGroups';
import useSearchResultWithFilters from '@shared/hooks/searchFilters/useSearchResultWithFilters';
import { CompanyTab } from '@shared/types/company';
import { FeatureName } from '@shared/types/planRestriction';
import BaseButton from '@shared/uikit/Button/BaseButton';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import Skeleton from '@shared/uikit/Skeleton';
import Switch from '@shared/uikit/Switch';
import useMedia from '@shared/uikit/utils/useMedia';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { convertCompanyTabToBEParam } from '@shared/utils/toolkit/convertCompanyTabToBEParam';
import CompanySearchCard from './partials/components/CompanySearchCard';
import { CustomSearchFilters } from './partials/components/CustomSearchFilters';
import useGetCompanyActiveTab from './partials/hooks/useGetCompanyActiveTab';
import CompanySearchResultDetails from './SearchPages.details';
import type { JSX } from 'react';

const SearchPages = (): JSX.Element => {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const activeTab = useGetCompanyActiveTab();
  const groups = useCompaniesFilterGroups();
  const { handleChangeParams, allParams } = useCustomParams();
  const onlyClients = allParams.onlyClients === 'true';
  const visibleSwitch =
    activeTab === CompanyTab.REQUESTS || activeTab === CompanyTab.PENDING;
  const myCompanyRole = getMyCompanyRole(allParams.onlyClients, activeTab);
  const { currentEntityId } = allParams;

  const {
    totalElements,
    totalPages,
    content = [],
    isEmpty,
    setPage,
    isFetching,
    isFetchingFilters,
  } = useSearchResultWithFilters({
    entity: 'companies',
    extraParams: {
      tab: convertCompanyTabToBEParam(activeTab),
      filter: visibleSwitch
        ? onlyClients
          ? 'INCLUDE_CLIENT'
          : 'INCLUDE_VENDOR'
        : undefined,
    },
  });

  const emptyMsgObj = {
    [CompanyTab.ALL]: {
      title: 'no_result_f',
      text: 'try_refining_search',
    },
    [CompanyTab.VENDORS]: {
      title: 'y_dt_h_a_vendor',
      text: 'lets_add_a_vendor_t_start_rec_j',
    },
    [CompanyTab.CLIENTS]: {
      title: 'y_dt_h_a_client',
      text: 'lets_add_a_client_t_start_rec_j',
    },
    [CompanyTab.PENDING]: {
      title: 'y_dt_h_a_request',
      text: 'y_need_t_send_req_t_a_c_f',
    },
    [CompanyTab.REQUESTS]: {
      title: 'y_dt_h_a_request',
      text: 'another_c_n_t_s_a_req_f',
    },
  }[activeTab];

  return (
    <SearchListWithDetailsLayout
      isFullWidth
      groups={groups}
      isLoading={isFetchingFilters}
      isTotalyEmpty={Boolean(isEmpty && !isFetching && !visibleSwitch)}
      FiltersComponent={CustomSearchFilters}
      sectionTotalEmpty={
        <EmptySectionInModules
          isFullParent
          isFullWidth
          title={t(emptyMsgObj.title)}
          text={t(emptyMsgObj.text)}
        />
      }
      listTopComponent={
        <PlanRestrictionCard
          className="mt-20"
          featuresName={[FeatureName.SEARCH_COMPANIES]}
        />
      }
      listComponent={
        <SearchList
          entity="pages"
          title={
            visibleSwitch
              ? onlyClients
                ? t('client_list')
                : t('vendor_list')
              : t('companies')
          }
          isLoading={isFetching}
          totalElements={totalElements}
          data={content}
          totalPages={totalPages}
          onPageChange={setPage as any}
          noTopBottomPadding
          noItemButtonAction={isMoreThanTablet}
          emptyList={
            visibleSwitch && (
              <EmptyState
                message={t('no_data')}
                caption=""
                className="h-full flex justify-center items-center"
              />
            )
          }
          renderItem={(item, index, props) => (
            <BaseButton
              onClick={() =>
                handleChangeParams({ add: { currentEntityId: item.id } })
              }
            >
              <CompanySearchCard item={item} activeTab={activeTab} {...props} />
            </BaseButton>
          )}
          headerElement={
            isFetching ? (
              <Skeleton className="rounded !w-[120px] !h-[22px] !mt-[9px]" />
            ) : visibleSwitch ? (
              <Switch
                value={onlyClients}
                onChange={() => {
                  handleChangeParams({
                    add: !onlyClients ? { onlyClients: 'true' } : undefined,
                    remove: onlyClients
                      ? ['onlyClients', 'currentEntityId']
                      : ['currentEntityId'],
                  });
                }}
                className="gap-8"
                label={t('client_list')}
                labelProps={{
                  color: 'secondaryDisabledText',
                  font: '400',
                  className: '!whitespace-nowrap',
                }}
              />
            ) : null
          }
        />
      }
      detailsComponent={
        <CompanySearchResultDetails
          activeTab={activeTab}
          isLoading={isFetching || isFetchingFilters}
          params={{
            pageId: currentEntityId,
            tab: convertCompanyTabToBEParam(activeTab),
            role: myCompanyRole,
          }}
          currentEntityId={currentEntityId}
          myCompanyRole={myCompanyRole}
        />
      }
    />
  );
};

export default SearchPages;
