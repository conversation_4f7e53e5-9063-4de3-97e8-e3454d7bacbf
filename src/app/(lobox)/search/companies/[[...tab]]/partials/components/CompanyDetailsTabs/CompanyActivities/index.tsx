import { type FC } from 'react';
import ActivityItem from '@shared/components/molecules/ActivityItem';
import ActivityItemSkeleton from '@shared/components/molecules/ActivityItem/ActivityItemSkeleton';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import Section from '@shared/components/molecules/Section';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import { getActivitiesOfVendorClients } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import { useDebounceState } from '@shared/utils/hooks/useDebounceState';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { ActivityProps } from '@shared/types/activityProps';
import type { BaseCandidateSectionProp } from '@shared/types/candidates';

const CandidateActivitiesTab: FC<BaseCandidateSectionProp> = ({ company }) => {
  const { t } = useTranslation();
  const keyword = useDebounceState('', 500);

  const infiniteQuery = useReactInfiniteQuery<ActivityProps>(
    [QueryKeys.getActivitiesOfVendorClients, company.id, keyword.debounceValue],
    {
      func: getActivitiesOfVendorClients,
      size: 20,
      extraProps: {
        vendorClientId: company.vendorClientId,
        text: keyword.debounceValue,
      },
    },
    {
      enabled: !!company.vendorClientId,
      refetchOnWindowFocus: false,
    }
  );

  const activities = (infiniteQuery.data ?? []).map((act) => ({
    ...act,
    user: act.user ?? {},
  }));

  return (
    <Section
      title={t('activities')}
      hasSearchInput
      onSearchChanged={keyword.setValue}
      searchInputProps={{ size: undefined }}
      className="!flex-1"
    >
      {infiniteQuery.isLoading ? (
        <CardWrapper>
          <ActivityItemSkeleton />
          <ActivityItemSkeleton />
          <ActivityItemSkeleton />
        </CardWrapper>
      ) : activities.length > 0 ? (
        <CardWrapper>
          {activities.map((activity) => (
            <ActivityItem key={activity.id} item={activity} />
          ))}
        </CardWrapper>
      ) : (
        <EmptySearchResult
          title={t('no_activities')}
          sectionMessage={t('no_activities_for_candidate_desc')}
          className="!py-32 px-0 !m-0"
        />
      )}
    </Section>
  );
};

export default CandidateActivitiesTab;
