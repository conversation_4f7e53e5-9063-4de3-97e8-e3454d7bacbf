import React from 'react';
import Button from '@shared/uikit/Button';
import useTranslation from '@shared/utils/hooks/useTranslation';

export function ClientVendorPendingBadge() {
  const { t } = useTranslation();

  return (
    <Button
      schema="orange-semi-transparent"
      label={t('requested')}
      leftIcon="info-circle"
      leftType="far"
      labelSize={12}
      className="!px-8 hover:!bg-pendingOrange_10 mb-auto"
      variant="thin"
    />
  );
}
