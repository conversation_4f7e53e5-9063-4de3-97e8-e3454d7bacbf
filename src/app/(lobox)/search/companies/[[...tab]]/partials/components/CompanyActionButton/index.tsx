import { useQueryClient } from '@tanstack/react-query';
import React from 'react';
import NineDotPanelSteps from '@shared/constants/NineDotPanelSteps';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import useResponseToast from '@shared/hooks/useResponseToast';
import { openNineDotPanel } from '@shared/stores/nineDotPanelStore';
import { CompanyTab } from '@shared/types/company';
import Button from '@shared/uikit/Button';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Flex from '@shared/uikit/Flex/index';
import InfoCard from '@shared/uikit/InfoCard';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import * as CompanyApi from '@shared/utils/api/company';
import { routeNames } from '@shared/utils/constants';
import QueryKeys from '@shared/utils/constants/queryKeys';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useUpdateQueryData from '@shared/utils/hooks/useUpdateQueryData';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import type { CompanyType, ECompanyRole } from '@shared/types/company';
import type { ButtonProps } from '@shared/uikit/Button';
import type { InfoCardProps } from '@shared/uikit/InfoCard';

type ActionProps = (ButtonProps | InfoCardProps) & {
  type?: 'info' | 'button';
  routName?: string;
  fullWidth?: boolean;
};

type CompanyActionButtonProps = {
  company: CompanyType;
  currentEntityId: string;
  activeTab: CompanyTab;
  badges: React.ReactNode;
  role: ECompanyRole;
  refetchDetails: VoidFunction;
};

const CompanyActionButton: React.FC<CompanyActionButtonProps> = ({
  company,
  currentEntityId,
  activeTab,
  badges,
  refetchDetails,
}) => {
  const { clientStatus, vendorStatus } = company;
  const { t } = useTranslation();
  const { handleSuccess } = useResponseToast();
  const { handleChangeParams } = useCustomParams();
  const { openConfirmDialog } = useOpenConfirm({ variant: 'normal' });
  const queryClient = useQueryClient();
  const { refetch: refetchPending } = useUpdateQueryData([
    QueryKeys.getPendingAndRequestCount,
  ]);
  const updateListQueries = () => {
    const queries = queryClient
      .getQueryCache()
      .findAll({ queryKey: [QueryKeys.searchCompanies] });
    queries.forEach((query) => {
      const key = query.queryKey;
      queryClient.refetchQueries({ queryKey: key });
    });
  };

  const onSuccess = () => {
    if ([CompanyTab.ALL].includes(activeTab)) {
      refetchDetails();
    } else {
      handleChangeParams({ remove: ['currentEntityId'] });
      updateListQueries();
    }
    refetchPending();
    handleSuccess();
  };
  const { mutate: addClient } = useReactMutation({
    apiFunc: CompanyApi.addClient,
    onSuccess,
  });
  const { mutate: addVendor } = useReactMutation({
    apiFunc: CompanyApi.addVendor,
    onSuccess,
  });

  const { mutate: cancelRequest } = useReactMutation({
    apiFunc: CompanyApi.cancelRequest,
    onSuccess,
  });
  const { mutate: declineRequest } = useReactMutation({
    apiFunc: CompanyApi.declineRequest,
    onSuccess,
  });
  const { mutate: acceptRequest } = useReactMutation({
    apiFunc: CompanyApi.acceptRequest,
    onSuccess,
  });

  const handleAddAsClient = (clientId: string) => {
    openConfirmDialog({
      message: translateReplacer(t('confirm_add_company_as_relation'), [
        'client',
      ]),
      title: t('add_client'),
      confirmButtonText: t('confirm'),
      cancelButtonText: t('discard'),
      isAjaxCall: true,
      apiProps: {
        func: addClient,
        variables: {
          clientId,
        },
      },
    });
  };
  const handleAddAsVendor = (vendorId: string) => {
    openConfirmDialog({
      message: translateReplacer(t('confirm_add_company_as_relation'), [
        'vendor',
      ]),
      title: t('add_vendor'),
      confirmButtonText: t('confirm'),
      cancelButtonText: t('discard'),
      isAjaxCall: true,
      apiProps: {
        func: addVendor,
        variables: {
          vendorId,
        },
      },
    });
  };
  const handleCancelRequest = (vendorClientId: string) => {
    openConfirmDialog({
      message: t('confirm_cancel_request_to_company'),
      title: t('cancel_request'),
      confirmButtonText: t('confirm'),
      cancelButtonText: t('discard'),
      isAjaxCall: true,
      apiProps: {
        func: cancelRequest,
        variables: {
          id: vendorClientId,
        },
      },
    });
  };
  const handleAcceptRequest = (vendorClientId: string) => {
    acceptRequest({ id: vendorClientId });
  };
  const handleDeclineRequest = (vendorClientId: string) => {
    openConfirmDialog({
      message: translateReplacer(t('confirm_decline_pending_request'), [
        company.title,
        t('vendor'),
      ]),
      title: t('decline_request'),
      confirmButtonText: t('confirm'),
      cancelButtonText: t('discard'),
      isAjaxCall: true,
      apiProps: {
        func: declineRequest,
        variables: {
          id: vendorClientId,
        },
      },
    });
  };

  const handleSubmitJob = () => {
    openMultiStepForm({
      formName: 'submitJob',
      data: {
        step: '1',
        vendorId: company.id,
      },
    });
  };

  const handleSubmitCandidate = () => {
    openMultiStepForm({
      formName: 'submitCandidate',
      data: {
        vendorId: company.id,
      },
    });
  };
  const handleContactCompany = () => {
    openNineDotPanel({
      isOpen: true,
      defaultActiveStep: NineDotPanelSteps.COMING,
    });
  };

  const actionMap: Record<string, ActionProps[]> = {
    all_NOT_REQUESTED_NOT_REQUESTED: [
      {
        label: t('add_as_client'),
        schema: 'semi-transparent',
        onClick: () => handleAddAsClient(company.id),
      },
      {
        label: t('add_as_vendor'),
        onClick: () => handleAddAsVendor(company.id),
      },
    ],
    all_APPROVED_NOT_REQUESTED: [
      {
        label: t('manage_client'),
        schema: 'semi-transparent',
        // onClick: () => handleAddAsClient(company.id),
      },
      {
        label: t('add_as_vendor'),
        onClick: () => handleAddAsVendor(company.id),
      },
    ],
    all_APPROVED_REQUESTED: [
      {
        label: t('manage_client'),
        schema: 'semi-transparent',
        fullWidth: true,
        // onClick: () => handleAddAsClient(company.id),
      },
      {
        label: translateReplacer(t('y_h_a_req_t_b_name_otc'), ['vendor']),
        type: 'info',
        fullWidth: true,
        routName: routeNames.companies.requests,
      },
    ],
    all_NOT_REQUESTED_APPROVED: [
      {
        label: t('add_as_client'),
        schema: 'semi-transparent',
        onClick: () => handleAddAsClient(company.id),
      },
      {
        label: t('manage_vendor'),
        // onClick: () => handleAddAsVendor(company.id),
      },
    ],
    all_APPROVED_APPROVED: [
      {
        label: t('manage_client'),
        schema: 'semi-transparent',
        to: routeNames.companies.clients,
      },
      {
        label: t('manage_vendor'),
        to: routeNames.companies.vendors,
      },
    ],
    all_REQUESTED_NOT_REQUESTED: [
      {
        label: translateReplacer(t('y_h_a_req_t_b_name_otc'), ['client']),
        type: 'info',
        fullWidth: true,
        routName: routeNames.companies.requests,
      },
      {
        label: t('add_as_vendor'),
        onClick: () => handleAddAsVendor(company.id),
        fullWidth: true,
      },
    ],
    all_NOT_REQUESTED_REQUESTED: [
      {
        label: translateReplacer(t('y_h_a_req_t_b_name_otc'), ['vendor']),
        type: 'info',
        fullWidth: true,
        routName: routeNames.companies.requests,
      },
      {
        label: t('add_as_client'),
        onClick: () => handleAddAsClient(company.id),
        fullWidth: true,
      },
    ],
    all_REQUESTED_REQUESTED: [
      {
        type: 'info',
        label: translateReplacer(t('y_h_a_req_t_b_name_otc'), ['client']),
        fullWidth: true,
        routName: routeNames.companies.requests,
      },
      {
        type: 'info',
        label: translateReplacer(t('y_h_a_req_t_b_name_otc'), ['vendor']),
        fullWidth: true,
        routName: routeNames.companies.requests,
      },
    ],
    all_PENDED_NOT_REQUESTED: [
      {
        type: 'info',
        fullWidth: true,
        label: translateReplacer(t('name_has_already_rytba_v_otc'), [
          company.title,
          t('vendor'),
        ]),
        routName: routeNames.companies.pending,
      },
      {
        label: t('add_as_client'),
        schema: 'semi-transparent',
        onClick: () => handleAddAsClient(company.id),
      },
      {
        label: t('add_as_vendor'),
        onClick: () => handleAddAsVendor(company.id),
      },
    ],
    all_PENDED_PENDED: [
      {
        type: 'info',
        fullWidth: true,
        label: translateReplacer(t('name_has_already_rytba_v_otc'), [
          company.title,
          t('vendor'),
        ]),
        routName: routeNames.companies.pending,
      },
      {
        type: 'info',
        fullWidth: true,
        label: translateReplacer(t('name_has_already_rytba_v_otc'), [
          company.title,
          t('client'),
        ]),
        routName: routeNames.companies.pending,
      },
    ],
    all_NOT_REQUESTED_PENDED: [
      {
        type: 'info',
        fullWidth: true,
        label: translateReplacer(t('name_has_already_rytba_v_otc'), [
          company.title,
          t('client'),
        ]),
        routName: routeNames.companies.pending,
      },
      {
        label: t('add_as_client'),
        schema: 'semi-transparent',
        onClick: () => handleAddAsClient(company.id),
      },
      {
        label: t('add_as_vendor'),
        onClick: () => handleAddAsVendor(company.id),
      },
    ],
    // Mohammad should check it
    all_REQUESTED_PENDED: [
      {
        type: 'info',
        fullWidth: true,
        label: translateReplacer(t('company_h_a_req_y_t_b_a_name_otc'), [
          company.title,
          t('client'),
        ]),
      },
      {
        type: 'info',
        label: translateReplacer(t('y_h_a_req_t_b_name_otc'), ['client']),
        fullWidth: true,
      },
    ],
    // Mohammad should check it
    all_PENDED_REQUESTED: [
      {
        type: 'info',
        fullWidth: true,
        label: translateReplacer(t('company_h_a_req_y_t_b_a_name_otc'), [
          company.title,
          t('client'),
        ]),
        routName: routeNames.companies.pending,
      },
      {
        type: 'info',
        fullWidth: true,
        label: translateReplacer(t('y_h_a_req_t_b_name_otc'), ['client']),
        routName: routeNames.companies.pending,
      },
    ],
    requests_NOT_REQUESTED_REQUESTED: [
      {
        type: 'info',
        fullWidth: true,
        label: translateReplacer(t('your_name_req_hbs_aipoap'), [t('vendor')]),
      },
      {
        label: t('cancel_request'),
        schema: 'semi-transparent',
        onClick: () => handleCancelRequest(company.vendorClientId),
        fullWidth: true,
      },
    ],
    requests_REQUESTED_NOT_REQUESTED: [
      {
        fullWidth: true,
        type: 'info',
        label: translateReplacer(t('your_name_req_hbs_aipoap'), [t('client')]),
      },
      {
        label: t('cancel_request'),
        schema: 'semi-transparent',
        onClick: () => handleCancelRequest(company.vendorClientId),
        fullWidth: true,
      },
    ],
    pending_PENDED_NOT_REQUESTED: [
      {
        type: 'info',
        label: translateReplacer(t('requested_tbavfyc'), [t('vendor')]),
        fullWidth: true,
      },
      {
        label: t('decline'),
        schema: 'semi-transparent',
        onClick: () => handleDeclineRequest(company.vendorClientId),
      },
      {
        label: t('accept'),
        onClick: () => handleAcceptRequest(company.vendorClientId),
      },
    ],
    pending_NOT_REQUESTED_PENDED: [
      {
        type: 'info',
        fullWidth: true,
        label: translateReplacer(t('requested_tbavfyc'), [t('client')]),
      },
      {
        label: t('cancel_request'),
        schema: 'semi-transparent',
        onClick: () => handleCancelRequest(company.vendorClientId),
      },
      {
        label: t('accept'),
        onClick: () => handleAcceptRequest(company.vendorClientId),
      },
    ],
    [CompanyTab.VENDORS]: [
      {
        label: t('contact'),
        schema: 'semi-transparent',
        onClick: handleContactCompany,
      },
      {
        label: t('submit_job'),
        onClick: handleSubmitJob,
      },
    ],
    [CompanyTab.CLIENTS]: [
      {
        label: t('contact'),
        schema: 'semi-transparent',
        onClick: handleContactCompany,
      },
      {
        label: t('submit_candidate'),
        onClick: handleSubmitCandidate,
        // disabled: Number(company.jobCount) === 0,
      },
    ],
  };

  const key = [CompanyTab.VENDORS, CompanyTab.CLIENTS].includes(activeTab)
    ? activeTab
    : `${activeTab}_${clientStatus}_${vendorStatus}`;
  const actions = actionMap[key] || [];

  return (
    <Flex className="gap-20">
      {badges}
      <Typography>{`${activeTab}_${clientStatus}_${vendorStatus} #${company.vendorClientId}`}</Typography>
      <Flex className="!grid grid-cols-2 gap-12">
        {actions.map(({ type, label, fullWidth, routName, ...rest }, key) =>
          type === 'info' ? (
            <InfoCard
              key={`action-${key}`}
              hasLeftIcon
              leftIconProps={{
                name: 'info-circle',
                color: 'secondaryDisabledText',
              }}
              classNames={{
                wrapper: cnj(
                  '!bg-gray_5 grid grid-cols-[1fr_auto]',
                  fullWidth && 'col-span-2'
                ),
              }}
              renderRight={
                routName
                  ? () => (
                      <Button
                        to={`${routName}?currentEntityId=${currentEntityId}`}
                        className="justify-self-end"
                        label={t('view_request')}
                        schema="transparent-brand"
                      />
                    )
                  : undefined
              }
            >
              <Typography size={14} color="secondaryDisabledText">
                {label}
              </Typography>
            </InfoCard>
          ) : (
            <Button
              className={cnj(fullWidth && '!grid col-span-2')}
              {...rest}
              label={label}
              key={`action-${key}`}
            />
          )
        )}
      </Flex>
    </Flex>
  );
};

export default CompanyActionButton;
