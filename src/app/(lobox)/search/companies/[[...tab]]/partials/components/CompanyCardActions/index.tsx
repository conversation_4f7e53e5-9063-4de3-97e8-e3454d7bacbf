import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { type FC } from 'react';
import Menu from '@shared/components/molecules/Menu/Menu';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import useResponseToast from '@shared/hooks/useResponseToast';
import { CompanyTab } from '@shared/types/company';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Flex from '@shared/uikit/Flex/index';
import InfoCard from '@shared/uikit/InfoCard';
import Typography from '@shared/uikit/Typography';
import { removeClient, removeVendor } from '@shared/utils/api/company';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { CompanyType } from '@shared/types/company';
import type { MenuItem } from '@shared/types/components/Menu.type';

export interface CompanyCardActionsProps {
  company: CompanyType;
  activeTab: CompanyTab;
}

const CompanyCardActions: FC<CompanyCardActionsProps> = (props) => {
  const { company, activeTab } = props;
  const { t } = useTranslation();
  const { handleChangeParams } = useCustomParams();
  const { handleSuccess } = useResponseToast();
  const queryClient = useQueryClient();
  const isVendor = activeTab === CompanyTab.VENDORS;
  const { openConfirmDialog } = useOpenConfirm();
  const updateListQueries = () => {
    const queries = queryClient
      .getQueryCache()
      .findAll({ queryKey: [QueryKeys.searchCompanies] });
    queries.forEach((query) => {
      const key = query.queryKey;
      queryClient.refetchQueries({ queryKey: key });
    });
  };

  const { mutate: removeVendorClientMutate } = useMutation({
    mutationFn: isVendor ? removeVendor : removeClient,
    onSuccess: () => {
      handleSuccess();
      updateListQueries();
      handleChangeParams({ remove: ['currentEntityId'] });
    },
  });

  const openDeleteConfirmation = () => {
    openConfirmDialog({
      title: t(`${isVendor ? 'remove_vendor' : 'remove_client'}`),
      message: (
        <Flex flexDir="column" className="gap-20">
          <InfoCard
            hasLeftIcon
            leftIconProps={{
              name: 'exclamation-triangle',
              color: 'warning',
            }}
            classNames={{ wrapper: '!bg-warning_10 ' }}
            label={t('plz_note_tiaimac_pro_w_cau')}
          />
          <Typography>
            {translateReplacer(t('r_y_s_y_w_t_r_t_name'), [
              isVendor ? 'vendor' : 'client',
            ])}
          </Typography>
        </Flex>
      ),
      confirmButtonText: t('remove'),
      cancelButtonText: t('discard'),
      confirmCallback: () => {
        removeVendorClientMutate({
          id: company.vendorClientId,
        });
      },
    });
  };

  const handleSubmitJob = () => {
    openMultiStepForm({
      formName: 'submitJob',
      data: {
        step: '1',
        vendorId: company.id,
      },
    });
  };

  const actions: MenuItem[] = [
    {
      iconName: 'pen-light',
      label: t('edit_submissions'),
      onClick: handleSubmitJob,
    },
    {
      iconName: 'trash1',
      label: isVendor ? t('remove_vendor') : t('remove_client'),
      onClick: openDeleteConfirmation,
    },
  ];

  return (
    <Menu
      menuItems={actions}
      menuPlacement="bottom-end"
      menuItemSize={20}
      classNames={{ itemIconWrapper: '!m-0', menu: '!p-0' }}
    />
  );
};

export default CompanyCardActions;
