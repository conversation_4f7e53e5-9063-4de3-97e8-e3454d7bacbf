import ActivityItemSkeleton from '@shared/components/molecules/ActivityItem/ActivityItemSkeleton';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import ObjectSectionContainerSkeleton from '@shared/components/molecules/Section/Section.skeleton';

const CandidateActivitesSkeleton = () => (
  <ObjectSectionContainerSkeleton hasSearch hasTitle>
    <CardWrapper>
      <ActivityItemSkeleton />
      <ActivityItemSkeleton />
      <ActivityItemSkeleton />
    </CardWrapper>
  </ObjectSectionContainerSkeleton>
);

export default CandidateActivitesSkeleton;
