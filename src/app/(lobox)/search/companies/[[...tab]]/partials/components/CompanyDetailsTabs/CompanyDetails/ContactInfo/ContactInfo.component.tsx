import React from 'react';
import BaseButton from 'shared/uikit/Button/BaseButton';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { callTo, mailTo } from '@shared/utils/toolkit/linking';
import Typography from '@shared/uikit/Typography';
import checkLinkPrefix from '@shared/utils/toolkit/checkLinkPrefix';
import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import InfoCard from '@shared/components/Organism/Objects/Common/InfoCard';
import DisplayPhoneNumber from '@shared/uikit/PhoneInput/DisplayPhoneNumber';

const ContactInfo: React.FC = ({ company }) => {
  const { t } = useTranslation();
  const { email, phone, link } = company;

  const data = [
    {
      id: 'email',
      title: t('email_address'),
      subTitle: t('no_email_ent'),
      icon: 'envelope',
      value: email ? (
        <BaseButton href={mailTo(email)}>
          <Typography
            size={15}
            color="thirdText"
            mt={5}
            height={21}
            isTruncated
          >
            {email}
          </Typography>
        </BaseButton>
      ) : undefined,
    },
    {
      id: 'phone',
      title: t('phone'),
      subTitle: t('no_p_number_ent'),
      value: phone ? (
        <BaseButton href={callTo(phone)}>
          <DisplayPhoneNumber
            // inputClassName={classes.phoneStyle}
            value={phone}
          />
        </BaseButton>
      ) : undefined,
      icon: 'phone',
      to: 'settings',
    },
    {
      id: 'link',
      title: t('website'),
      subTitle: t('no_web_ent'),
      value: link ? (
        <BaseButton href={checkLinkPrefix(link)} target="_blank">
          <Typography
            size={15}
            color="thirdText"
            mt={5}
            height={21}
            isTruncated
          >
            {link}
          </Typography>
        </BaseButton>
      ) : undefined,
      icon: 'link',
    },
  ];

  return (
    <SectionLayout
      title={t('contact')}
      classNames={{ childrenWrap: '!p-20 gap-12' }}
      visibleActionButton={false}
    >
      {data.map(({ id, title, subTitle, value, icon }) => (
        <InfoCard
          key={id}
          disabledHover
          title={title}
          subTitle={subTitle}
          value={value}
          icon={icon}
          iconSize={16}
          wrapperClassName="!p-0"
        />
      ))}
    </SectionLayout>
  );
};

export default ContactInfo;
