import type { ReviewVisibilityType } from '@shared/utils/constants/enums/candidateDb';
import type { UserTypeInList } from './user';
import type { ValueLabelIconType } from './general';
import { JobParticipationModel } from './jobsProps';

export interface ReviewRequest {
  text: string;
  score: number;
  visibility: ReviewVisibilityType;
}

export interface BEReview extends ReviewRequest {
  id: string;
  createdDate: string;
  user: UserTypeInList;
}

export interface IReview {
  id: string;
  user: UserTypeInList;
  score: number;
  text: string;
  createdDate: string;
  visibility: ValueLabelIconType<ReviewVisibilityType>;
}

export type ReviewProps = BEReview;
export type JobReviewProps = ReviewProps & {
  participation: JobParticipationModel;
};
