import type { <PERSON><PERSON><PERSON> } from 'shared/types/job';
import type { searchFilterQueryParams } from 'shared/constants/search';
import type {
  NetworkModelType,
  PageCategoryType,
  PageCompanySizeType,
  PageStatusType,
} from './page';
import type { EntityUserType } from './common';
import type { AppEntitiesType } from './general';
import { BELocation } from './lookup';

export type JobKeyType = keyof IJob;

export type SearchFiltersQueryParamsType = keyof typeof searchFilterQueryParams;
export type SearchFiltersValueType = Array<string> | string;

export interface ISearchPeopleApi {
  id: string;
  userType: EntityUserType;
  username: string;
  name: string;
  surname: string;
  occupationId: string;
  occupationName: string;
  imageUrl?: string;
  croppedImageUrl?: string;
  birthDate?: string;
  title?: string;
  status?: string;
  category?: string;
  industryLookupId?: string;
  industryName: string;
  createdDate: string;
  lastModifiedDate?: string;
  establishmentDate?: string;
  establishmentYear?: string;
  experiencesAtCompanyIds?: string[] | string;
  educatedAtSchoolIds?: string[] | string;
  countryIds?: string[] | string;
  cityIds?: string[] | string;
  networkModel: NetworkModelType;
  hideIt: boolean;
  youHaveBlocked?: boolean;
}

export interface ISearchPeople extends ISearchPeopleApi {
  fullName: string;
  subTitle: string;
  type: EntityUserType;
  usernameAtSign?: string;
  isPage?: boolean;
}

export interface ISuggestObjectApi {
  id: string;
  userType: AppEntitiesType;
  username: string;
  name?: string;
  surname?: string;
  occupationId?: string;
  occupationName?: string;
  imageUrl: string;
  croppedImageUrl: string;
  birthDate?: string;
  title: string;
  status: string;
  category: string;
  industryLookupId: string;
  industryName: string;
  createdDate?: string;
  lastModifiedDate?: string;
  establishmentDate: string;
  establishmentYear: string;
  experiencesAtCompanyIds?: Array<string>;
  educatedAtSchoolIds?: Array<string>;
  countryIds?: Array<string>;
  cityIds?: Array<string>;
  networkModel?: NetworkModelType;
  hideIt: boolean;
  youAreBlocked?: boolean;
  youHaveBlocked?: boolean;
}
export interface ISuggestObject extends ISuggestObjectApi {
  fullName: string;
  subTitle: string;
  type: EntityUserType;
}

export interface ISuggestCompany {
  id: number;
  username: string;
  title: string;
  description: string;
  ownerId: number;
  establishmentDate: string;
  status: PageStatusType;
  imageUrl: string;
  croppedImageUrl: string;
  headerImageUrl: string;
  croppedHeaderImageUrl: string;
  category: PageCategoryType;
  companySize: PageCompanySizeType;
  industryLookupId: number;
  industryName: string;
  languageLookupId: number;
  languageName: string;
  hashtags: string[];
  email: string;
  locationModels: BELocation[];
  allowOthersToShareYourPosts: boolean;
  allowOthersToTagOrMentionYou: boolean;
  adultContent: boolean;
}

export type ISuggestHashtagApi = {
  id: string;
  usageCount: string;
};

export interface ISuggestHashtag {
  title: string;
  type: 'HASHTAG';
}

export interface ISearchPageApi {
  id: string;
  userType: string;
  username: string;
  name?: string;
  surname?: string;
  occupationId?: string;
  occupationName?: string;
  imageUrl: string;
  croppedImageUrl: string;
  birthDate?: string;
  title: string;
  status: string;
  category: string;
  industryLookupId: string;
  industryName: string;
  createdDate?: string;
  lastModifiedDate?: string;
  establishmentDate: string;
  establishmentYear: string;
  experiencesAtCompanyIds?: Array<string>;
  educatedAtSchoolIds?: Array<string>;
  countryIds?: Array<string>;
  cityIds?: Array<string>;
  networkModel: NetworkModelType;
  hideIt?: boolean;
}

export interface ISearchPage extends ISearchPageApi {
  type: EntityUserType;
  locationTitle?: string;
}

export interface ISearchHashtagApi {
  followersCounter?: string;
  followingsCounter?: string;
  follow?: boolean;
  id: string;
  usageCount?: string;
}

export interface ISearchHashtag extends ISearchHashtagApi {
  type: 'HASHTAG';
  title: string;
}

export interface ISearchPostApi {
  id: string;
  ownerId: string;
  ownerUserType: string;
  body: string;
  medias?: Array<string>;
  tags?: Array<string>;
  hashtags?: Array<string>;
  locationTitle?: string;
  locationLat?: string;
  locationLong?: string;
  locationDetail?: string;
  sharedOf: ISharedOf;
  highlighted: boolean;
  highlight?: string;
  hidden: boolean;
  ownerProfileInfo?: string;
  ownerPageInfo: IOwnerPageInfo;
  createdDate: string;
  lastModifiedDate: string;
  hideIt: boolean;
  follow?: string;
  likeActionCounter?: string;
  boostActionCounter?: string;
  celebrateActionCounter?: string;
  dislikeActionCounter?: string;
  notRelevantActionCounter?: string;
  commentCounter?: string;
  edited: boolean;
  deleted: boolean;
  sharedPage?: string;
  type?: string;
}
export interface ISharedOf {
  id: string;
  ownerId: string;
  ownerUserType: string;
  body?: string;
  medias?: Array<string>;
  tags?: Array<string>;
  hashtags?: Array<string>;
  locationTitle?: string;
  locationLat?: string;
  locationLong?: string;
  locationDetail?: string;
  sharedOf?: string;
  highlighted: boolean;
  highlight?: string;
  hidden: boolean;
  ownerProfileInfo: IOwnerProfileInfo;
  ownerPageInfo?: string;
  createdDate: string;
  lastModifiedDate: string;
  hideIt: boolean;
  follow?: string;
  likeActionCounter?: string;
  boostActionCounter?: string;
  celebrateActionCounter?: string;
  dislikeActionCounter?: string;
  notRelevantActionCounter?: string;
  commentCounter?: string;
  edited?: string;
  deleted: boolean;
  sharedPage?: string;
  type?: string;
}
export interface IOwnerProfileInfo {
  userId: string;
  username: string;
  name: string;
  surname: string;
  imageUrl: string;
  croppedImageUrl: string;
  occupationId: string;
  occupationName: string;
  privateProfile: boolean;
}
export interface IOwnerPageInfo {
  id: string;
  username: string;
  title: string;
  status: string;
  imageUrl: string;
  croppedImageUrl: string;
  headerImageUrl?: string;
  croppedHeaderImageUrl?: string;
  category: string;
  industryLookupId?: string;
  industryName: string;
}

export interface ISearchPost extends ISearchPostApi {
  type: 'POST';
}

export interface IUserMention {
  category: string;
  croppedImageUrl: string;
  followersCounter: number;
  followingsCounter: number;
  id: number;
  imageUrl: string;
  name: string;
  occupationName: string;
  surname: string;
  title: string;
  userType: EntityUserType;
  username: string;
}

export interface ISuggestPageMember {
  id: number;
  userType: EntityUserType;
  username: string;
  name: string;
  surname: string;
  title: string;
  croppedImageUrl: string;
  allowPageRoleAssign: boolean;
}
