import type { BeforeCachePageDetailType } from '@shared/types/page';
import { ISuggestCompany } from './search';

export enum EPageInfoStatus {
  UNPUBLISHED = 'UNPUBLISHED',
  PUBLISHED = 'PUBLISHED',
}

export enum EPageInfoAccess {
  ONLY_ME = 'ONLY_ME',
  MY_FOLLOWERS = 'MY_FOLLOWERS',
  EVERYONE_AT_LOBOX = 'EVERYONE_AT_LOBOX',
}

export enum ECompanyRole {
  CLIENT = 'CLIENT',
  VENDOR = 'VENDOR',
}

export enum ECompanyStatus {
  REQUESTED = 'REQUESTED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  REVOKED = 'REVOKED',
  CANCELED = 'CANCELED',
}

export interface BEGetClients {
  id: number;
  pageInfo: ISuggestCompany;
  companyRole: ECompanyRole;
  status: ECompanyStatus;
}

export enum CompanyTab {
  ALL = 'all',
  VENDORS = 'vendors',
  CLIENTS = 'clients',
  REQUESTS = 'requests',
  PENDING = 'pending',
}
export enum CompanyClientVendorStatus {
  REQUESTED = 'REQUESTED',
  APPROVED = 'APPROVED',
  PENDED = 'PENDED',
  NOT_REQUESTED = 'NOT_REQUESTED',
}

export type CompanyType = BeforeCachePageDetailType & {
  vendorClientId: string;
  candidateCount: string;
  jobCount: string;
  collaboratorCount: string;
  vendorId: string;
  clientId: string;
  vendorStatus: CompanyClientVendorStatus;
  clientStatus: CompanyClientVendorStatus;
  pageId?: string;
  companyRole: ECompanyRole;
  locationTitle?: string;
  vendorClientStatus?: 'REQUESTED' | 'APPROVED' | 'PENDED' | 'NOT_REQUESTED';
};
