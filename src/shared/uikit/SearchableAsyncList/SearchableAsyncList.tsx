/* eslint-disable jsx-a11y/click-events-have-key-events */
import type { ReactNode } from 'react';
import React, { useEffect, useState, useCallback, useRef, memo } from 'react';
import { Virtualizer, type VirtualizerHandle } from 'virtua';
import { useDebounceState } from 'shared/utils/hooks/useDebounceState';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import EmptyState from '@shared/components/Organism/EmptyState/EmptyState';
import BaseButton from 'shared/uikit/Button/BaseButton';
import useTranslation from '@shared/utils/hooks/useTranslation';
import DefaultLoading from './DefaultLoading';
import Flex from '../Flex';
import cnj from '../utils/cnj';

type GeneralItem = {
  id: string | number;
};

export interface SearchableAsyncListProps<T extends GeneralItem = GeneralItem> {
  name: string;
  value?: T | T[];
  variant: 'single' | 'multi';
  renderItem: (args: {
    item: T;
    isSelected: boolean;
    index: number;
    text?: string;
  }) => React.ReactNode;
  apiFunc?: (params: any) => Promise<any>;
  normalizer?: (data: any) => T[];
  keywords?: string;
  params?: Record<string, any>;
  initSearchValue?: string;
  hardRefetch?: boolean;
  onChange?: (value: T | T[] | undefined) => void;
  onChangeInput?: (input: string) => void;
  onOptions?: (options: T[]) => void;
  placeholder?: string;
  limit?: number;
  listItemClassName?: string;
  wrapperClassName?: string;
  searchClassName?: string;
  listItemsClassName?: string;
  hasSearch?: boolean;
  renderInfoMessage?: ReactNode;
  renderLoading?: ReactNode;
  renderEmpty?: ReactNode;
  renderNextPageLoading?: ReactNode;
  renderError?: ReactNode;
  scrollRef?: any;
  startMargin?: number;
  pageSize?: number;
  enableInfiniteScroll?: boolean;
  setTotalCount?: (value: string) => void;
  maxHeight?: number;
}

const SearchableAsyncList = <T extends GeneralItem = GeneralItem>({
  name,
  value: parentValue,
  variant,
  renderItem,
  normalizer,
  onChange: parentOnChange,
  onChangeInput: parentOnChangeInput,
  apiFunc,
  onOptions,
  renderInfoMessage,
  renderLoading,
  renderEmpty,
  renderNextPageLoading,
  renderError,
  listItemClassName,
  listItemsClassName,
  searchClassName,
  wrapperClassName,
  scrollRef,
  setTotalCount,
  maxHeight = 520,
  startMargin = 0,
  pageSize = 20,
  enableInfiniteScroll = false,
  params = {},
  hasSearch = true,
  keywords = 'text',
  hardRefetch = true,
  initSearchValue = '',
  placeholder = 'Search...',
  limit = variant === 'multi' ? 10 : 3,
}: SearchableAsyncListProps<T>) => {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState('');
  const [selectedItems, setSelectedItems] = useState<T[]>(() => {
    if (variant === 'multi') {
      return Array.isArray(parentValue) ? parentValue : [];
    }
    return parentValue ? [parentValue as T] : [];
  });

  const { debounceValue, setValue } = useDebounceState<string>(
    initSearchValue,
    500
  );

  const queryKey = [name, debounceValue, JSON.stringify(params)].filter(
    Boolean
  ) as string[];
  const virtRef = useRef<VirtualizerHandle>(null);
  const refetchCalled = useRef(false);
  const internalScrollRef = useRef<HTMLDivElement>(null);

  const wrappedApiFunc = useCallback(
    (queryParams: any) => {
      if (!apiFunc) return Promise.resolve({ content: [], last: true });

      const page = queryParams.page || 0;

      const formattedParams = {
        params: {
          [keywords]: debounceValue,
          ...params,
          page,
          size: pageSize,
        },
      };

      return apiFunc(formattedParams).then((response: any) => response as any);
    },
    [apiFunc, keywords, debounceValue, params, pageSize]
  );

  const generalApiFunc = useCallback(() => {
    if (!apiFunc) return Promise.resolve({ content: [], last: true });

    const formattedParams = {
      params: {
        [keywords]: debounceValue,
        ...params,
      },
    };

    return apiFunc(formattedParams).then((response: any) => response as any);
  }, [apiFunc, keywords, debounceValue, params]);

  const {
    data: infiniteFilteredOptions = [],
    fetchNextPage,
    isFetchingNextPage,
    status: infiniteStatus,
    hasNextPage,
    refetch: infiniteRefetch,
    error: infiniteError,
  } = useInfiniteQuery(
    queryKey,
    {
      func: wrappedApiFunc,
      size: pageSize,
    },
    {
      enabled: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      onSuccess: (data: any) => {
        const totalCount = data?.pages?.at(0)?.totalElements;
        const newItems = normalizer ? normalizer(data) : data;
        onOptions?.(newItems || []);
        setTotalCount?.(totalCount);
      },
      onError: (e: any) => {
        if (
          e?.response?.data?.error === 'ZeroPageIsNotCalledException' &&
          !refetchCalled.current
        ) {
          refetchCalled.current = true;
          refetch();
        }
      },
      getNextPageParam: (lastPage: any, allPages: any[]) => {
        if (
          lastPage?.last === false ||
          (lastPage?.content && lastPage.content.length === pageSize)
        ) {
          const nextPage = allPages.length + 1;
          return nextPage;
        }
        return undefined;
      },
    }
  );

  const {
    data: generalFilteredOptions = [],
    status: generalStatus,
    refetch: generalRefetch,
    error: generalError,
  } = useReactQuery({
    action: {
      key: queryKey,
      apiFunc: generalApiFunc,
    },
    config: {
      enabled: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      onSuccess: (data: any) => {
        const newItems = normalizer ? normalizer(data) : data;
        onOptions?.(newItems || []);
      },
      onError: (e: any) => {
        if (!refetchCalled.current) {
          refetchCalled.current = true;
          refetch();
        }
      },
    },
  });

  const filteredOptions = enableInfiniteScroll
    ? infiniteFilteredOptions
    : generalFilteredOptions;
  const status = enableInfiniteScroll ? infiniteStatus : generalStatus;
  const refetch = enableInfiniteScroll ? infiniteRefetch : generalRefetch;
  const error = enableInfiniteScroll ? infiniteError : generalError;

  const isLoading = status === 'loading';

  const onScroll = useCallback(() => {
    if (!enableInfiniteScroll) return;

    const handle = virtRef.current;
    if (!handle) {
      return;
    }

    const endIndex = handle.findEndIndex();

    if (
      hasNextPage &&
      !isFetchingNextPage &&
      endIndex >= filteredOptions.length - 3
    ) {
      fetchNextPage();
    }
  }, [
    enableInfiniteScroll,
    hasNextPage,
    isFetchingNextPage,
    filteredOptions.length,
    fetchNextPage,
  ]);

  const onScrollFallback = useCallback(
    (e: any) => {
      if (!enableInfiniteScroll) return;

      const { scrollTop, scrollHeight, clientHeight } = e.target;
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

      if (isNearBottom && hasNextPage && !isFetchingNextPage) {
        fetchNextPage();
      }
    },
    [enableInfiniteScroll, hasNextPage, isFetchingNextPage, fetchNextPage]
  );

  useEffect(() => {
    if (debounceValue || hardRefetch) {
      refetch();
    }
  }, [debounceValue, hardRefetch, refetch]);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement> | string) => {
      const { value } = typeof e === 'string' ? { value: e } : e.target;
      setInputValue(value);
      setValue(value);
      parentOnChangeInput?.(value);
    },
    [setValue, parentOnChangeInput]
  );

  const handleInputFocus = useCallback(() => {
    if (!filteredOptions.length) {
      refetch();
    }
  }, [filteredOptions.length, refetch]);

  const handleItemSelect = useCallback(
    (item: T) => {
      if (variant === 'single') {
        setSelectedItems([item]);
        parentOnChange?.(item);
      } else {
        const isAlreadySelected = selectedItems.some(
          (selected) => selected.id === item.id
        );

        let newSelection: T[];
        if (isAlreadySelected) {
          newSelection = selectedItems.filter(
            (selected) => selected.id !== item.id
          );
        } else if (selectedItems.length < limit) {
          newSelection = [...selectedItems, item];
        } else {
          return;
        }

        setSelectedItems(newSelection);
        parentOnChange?.(newSelection);
      }
    },
    [variant, selectedItems, limit, parentOnChange]
  );

  const isItemSelected = useCallback(
    (item: T) => selectedItems.some((selected) => selected.id === item.id),
    [selectedItems]
  );

  return (
    <Flex flexDir="column" className={wrapperClassName}>
      {hasSearch && (
        <SearchInputV2
          placeholder={placeholder}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          className={cnj('w-full', searchClassName)}
          value={inputValue}
        />
      )}

      {isLoading ? (
        renderLoading || <DefaultLoading />
      ) : error && renderError ? (
        renderError
      ) : !filteredOptions?.length ? (
        renderEmpty || (
          <EmptyState
            message={t('no_content_found')}
            caption=""
            className="h-full flex justify-center items-center"
          />
        )
      ) : (
        <>
          {!!renderInfoMessage && renderInfoMessage}

          {filteredOptions?.length && (
            <Flex flexDir="column" className={listItemsClassName}>
              {enableInfiniteScroll ? (
                <div
                  ref={internalScrollRef}
                  style={{ height: `${maxHeight}px`, overflow: 'auto' }}
                >
                  <Virtualizer
                    ref={virtRef}
                    scrollRef={scrollRef || internalScrollRef}
                    startMargin={startMargin}
                    onScroll={onScroll}
                  >
                    {filteredOptions?.map((item, index) => (
                      <BaseButton
                        key={item.id}
                        aria-selected={isItemSelected(item)}
                        onClick={() => handleItemSelect(item)}
                        className={cnj('cursor-pointer', listItemClassName)}
                      >
                        {renderItem({
                          item,
                          isSelected: isItemSelected(item),
                          index,
                          text: inputValue,
                        })}
                      </BaseButton>
                    ))}
                  </Virtualizer>
                </div>
              ) : (
                <Flex
                  flexDir="column"
                  className={cnj('overflow-y-auto', listItemsClassName)}
                  onScroll={onScrollFallback}
                >
                  {filteredOptions.map((item, index) => (
                    <div
                      key={item.id}
                      role="option"
                      aria-selected={isItemSelected(item)}
                      tabIndex={0}
                      onClick={() => handleItemSelect(item)}
                      className={cnj('cursor-pointer', listItemClassName)}
                    >
                      {renderItem({
                        item,
                        isSelected: isItemSelected(item),
                        index,
                        text: inputValue,
                      })}
                    </div>
                  ))}
                </Flex>
              )}
              {isFetchingNextPage &&
                renderNextPageLoading &&
                renderNextPageLoading}
            </Flex>
          )}
        </>
      )}
    </Flex>
  );
};

export default memo(SearchableAsyncList) as <
  T extends GeneralItem = GeneralItem,
>(
  props: SearchableAsyncListProps<T>
) => React.JSX.Element;
