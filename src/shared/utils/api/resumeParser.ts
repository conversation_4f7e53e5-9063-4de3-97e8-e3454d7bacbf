import resumeParser from 'shared/utils/constants/servicesEndpoints/services/resumeParser';
import request from '../toolkit/request';
import type { UploadFileProps } from './file';

export const uploadResumeProfile = ({
  file,
  append,
  onProgress,
  ...rest
}: UploadFileProps): Promise<any> => {
  const formData = new FormData();
  const extension = file.type.split('/')[1];
  const fileName = file.name ? file.name : `filename.${extension}`;
  formData.append('file', file, fileName);
  if (append) {
    formData.append('append', append.toString());
  }

  return request.upload(resumeParser.extractProfileDataFromResume, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent: any) => {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress?.(percentCompleted);
    },
    ...rest,
  });
};
