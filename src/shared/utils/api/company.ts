import beforeCachePageDetail from '@shared/utils/normalizers/beforeCachePageDetail';
import searchFiltersNormalizer from '@shared/utils/normalizers/searchFiltersNormalizer';
import { Endpoints } from '../constants';
import { companyEndPoints } from '../constants/servicesEndpoints';
import request from '../toolkit/request';
import type { BEGetClients, CompanyType } from '@shared/types/company';
import type { PaginateResponse } from '@shared/types/response';
import type { SuggestSubmitToVendor } from '@shared/types/submitVendor';

export type ISuggestCompanyFilter =
  | 'INCLUDE_VENDOR'
  | 'ONLY_APPROVED_VENDOR'
  | 'EXCLUDE_VENDOR'
  | 'INCLUDE_CLIENT'
  | 'ONLY_APPROVED_CLIENT'
  | 'EXCLUDE_CLIENT'
  | 'ONLY_NO_RELATION'
  | 'EXCLUDE_NO_RELATION'
  | 'ALL';

export interface ISuggestCompanyParams {
  text: string;
  filter: ISuggestCompanyFilter;
  jobId?: number;
}

export const getClients = async ({
  pageable,
}: {
  pageable: { page: number; size: number; sort: string[] };
}): Promise<PaginateResponse<BEGetClients>> => {
  const { data } = await request.get<PaginateResponse<BEGetClients>>(
    companyEndPoints.getClients,
    {
      params: {
        pageable,
      },
    }
  );

  return data;
};

export const getVendorsIncluded = async <T>(params?: {
  page?: number;
  size?: number;
}): Promise<PaginateResponse<T>> => {
  const { data } = await request.get<PaginateResponse<T>>(
    Endpoints.App.companyService.getVendorsIncluded,
    {
      params,
    }
  );

  return data;
};

export const getVendorsExcluded = async (id: string) => {
  const { data } = await request.get(
    Endpoints.App.companyService.getVendorsExcluded(id)
  );

  return data;
};

export const getSuggestCompany = async ({
  params,
}: {
  params?: {
    text: string;
    companyFilter: 'CLIENT' | 'ONLY_APPROVED_VENDOR' | 'ALL';
    jobId: number;
  };
} = {}): Promise<SuggestSubmitToVendor[]> => {
  const { data } = await request.get(Endpoints.App.search.getSuggestCompany, {
    params,
  });

  return data;
};

export const postMultiJobSubmit = async (params: {
  vendorIds: string[];
  jobId: number;
  description: string;
}): Promise<any> => {
  const { data } = await request.post(
    Endpoints.App.companyService.postMultiJobSubmit,
    {
      ...params,
    }
  );

  return data;
};

export const addVendors = async (params: any): Promise<any> => {
  const { data } = await request.post(companyEndPoints.addVendors, params);

  return data;
};
export const addVendor = async (params: any): Promise<any> => {
  const { data } = await request.post(companyEndPoints.addVendor, params);

  return data;
};

export const addClients = async (params: any): Promise<any> => {
  const { data } = await request.post(companyEndPoints.addClients, params);

  return data;
};

export const jobSubmit = async (params: {
  vendorId: number;
  jobId: number;
  description?: string;
}): Promise<any> => {
  const { data } = await request.post(companyEndPoints.jobSubmit, params);

  return data;
};

export const jobWithdraw = async (params: {
  vendorClientId: number;
  jobId: number;
  description: string;
}): Promise<any> => {
  const { data } = await request.post(companyEndPoints.jobWithdraw, params);

  return data;
};

export const addClient = async (params: any): Promise<any> => {
  const { data } = await request.post(companyEndPoints.addClient, params);

  return data;
};

export const submitCandidate = async (params: any): Promise<any> => {
  const { data } = await request.get(
    companyEndPoints.submitCandidate(params.vendorClientId),
    params
  );

  return data;
};

// pending
export const acceptRequest = async ({ id = '' }: { id?: string }) => {
  const { data } = await request.put(companyEndPoints.acceptRequest(id));

  return data;
};

// pending
export const declineRequest = async ({ id = '' }: { id?: string }) => {
  const { data } = await request.put(companyEndPoints.delcineRequest(id));

  return data;
};

// request
export const cancelRequest = async ({ id }) => {
  const { data } = await request.put(companyEndPoints.cancelRequest(id));

  return data;
};

export const getCompanyDetailInVendorClientByPageId = async ({
  params,
  accessToken,
}: {
  params: any;
  accessToken?: string;
}): Promise<CompanyType> => {
  if (!params.pageId) {
    throw new Error('pageId is required');
  }
  const { data } = await request.get(
    companyEndPoints.getCompanyDetailInVendorClientByPageId(params.pageId),
    {
      accessToken,
      params,
    }
  );

  return {
    ...beforeCachePageDetail(data),
    id: data.pageId,
    vendorClientId: data.id,
  };
};

export const getPendingAndRequestCount = async () => {
  const { data } = await request.get(
    companyEndPoints.getPendingAndRequestCount
  );

  return data;
};

export const suggestCompanies = async ({
  params,
}: {
  params: ISuggestCompanyParams;
}) => {
  const { data } = await request.get(companyEndPoints.suggestCompany, {
    params,
  });

  return data;
};

export const getSearchCompanyFilters = async ({ params }: { params: any }) => {
  const { data } = await request.get(companyEndPoints.searchCompanyFilters, {
    params,
  });

  return searchFiltersNormalizer.companyFilters(data);
};

export const removeVendor = async ({ id }) => {
  const { data } = await request.put(companyEndPoints.removeVendor(id));

  return data;
};
export const removeClient = async ({ id }) => {
  const { data } = await request.put(companyEndPoints.removeClient(id));

  return data;
};
