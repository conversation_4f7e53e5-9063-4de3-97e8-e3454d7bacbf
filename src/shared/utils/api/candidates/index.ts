import searchFiltersNormalizer from '@shared/utils/normalizers/searchFiltersNormalizer';
import {
  jobsEndpoints,
  candidateEndpoints,
  invitationEndpoints,
} from '../../constants/servicesEndpoints';
import beforeCacheCandidateInfo, {
  candidateSearchItemNormalizer,
  normalizeCandidateSummary,
} from '../../normalizers/beforeCacheCandidateInfo';
import beforeCacheUserInfo from '../../normalizers/beforeCacheUserInfo';
import request from '../../toolkit/request';
import {
  getKnownApiErrorData,
  isDuplicatedCandidateException,
} from '../utils/error';
import type { ActivityProps } from '@shared/types/activityProps';
import type {
  CandidateAPIData,
  CreateCandidateAPIRequestBody,
  CandidateSocialInfoAPIRequestBody,
  CandidateDemographicInfoAPIRequestBody,
  CandidateLegalInfoAPIRequestBody,
  CandidatePreferenceInfoAPIRequestBody,
  CandidateStatusInfoAPIRequestBody,
  CandidateFormData,
  BECandidateSearchResult,
  ICandidateListItemProps,
  CandidateResumeRequestBody,
  BECandidateSummary,
  ICandidateSummaryOptions,
} from '@shared/types/candidates';
import type { SendCandidateInvitationRequestBody } from '@shared/types/invitation';
import type { MeetingDataProps } from '@shared/types/meeting';
import type { ProjectProps } from '@shared/types/project';
import type { PaginateResponse } from '@shared/types/response';
import type { UserApiResponse } from '@shared/types/user';

export * from './todos';
export * from './notes';
export * from './reviews';
export * from './meetings';

export const getCandidatesList = async ({
  params,
}: {
  params?: {
    text?: string;
    page?: number;
    size?: number;
    onlyCandidates?: boolean;
  };
}): Promise<PaginateResponse<CandidateFormData>> => {
  const { data } = await request.get<PaginateResponse<CandidateAPIData>>(
    candidateEndpoints.suggestCandidates,
    {
      params,
    }
  );

  return {
    ...data,
    content: data.content.map(beforeCacheCandidateInfo),
  };
};

export const getSimilarCandidates = async (params: {
  id: string;
  page?: number;
  size?: number;
}): Promise<PaginateResponse<ReturnType<typeof beforeCacheUserInfo>>> => {
  const url = candidateEndpoints.similarCandidatesById(params.id);
  const { data } = await request.get<PaginateResponse<UserApiResponse>>(url, {
    params: {
      page: params.page,
      size: params.size,
    },
  });

  return {
    ...data,
    content: data.content.map(beforeCacheUserInfo),
  };
};

export const getCandidateById = async (
  id: string
): Promise<CandidateFormData> => {
  const url = candidateEndpoints.singleCandidateById(id);
  const { data } = await request.get<CandidateAPIData>(url);

  return beforeCacheCandidateInfo(data);
};

export const createCandidate = async (params: {
  body: CreateCandidateAPIRequestBody;
}): Promise<CandidateFormData> => {
  try {
    const { data } = await request.post<CandidateAPIData>(
      candidateEndpoints.createCandidate,
      params.body
    );

    return beforeCacheCandidateInfo(data);
  } catch (e: unknown) {
    const knownApiError = getKnownApiErrorData(e);
    if (knownApiError) {
      const duplicated = knownApiError.fieldErrors.find(
        isDuplicatedCandidateException
      );
      if (duplicated) {
        const candidate = await getCandidateById(duplicated.rejectedValue);

        return {
          ...candidate,
          _isDuplicatdOnCreate: true,
        };
      }
    }
    throw e;
  }
};

export const editCandidateBasic = async (params: {
  candidateId: string;
  body: CreateCandidateAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editBasicInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);

  return data;
};

export const editCandidateSocialInfo = async (params: {
  candidateId: string;
  body: CandidateSocialInfoAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editSocialInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);

  return data;
};

export const editCandidatePreferenceInfo = async (params: {
  candidateId: string;
  body: CandidatePreferenceInfoAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editPreferenceInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);

  return data;
};

export const editCandidateLegalInfo = async (params: {
  candidateId: string;
  body: CandidateLegalInfoAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editLegalInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);

  return data;
};

export const editCandidateDemographicInfo = async (params: {
  candidateId: string;
  body: CandidateDemographicInfoAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editDemographicInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);

  return data;
};

export const editCandidateAdditionalInfo = async (params: {
  candidateId: string;
  body: CandidateStatusInfoAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editAdditionalInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);

  return data;
};

export const editCandidateResume = async (params: {
  candidateId: string;
  body: CandidateResumeRequestBody;
}): Promise<{ data: any }> => {
  const url = candidateEndpoints.editResumeById(params.candidateId);
  const res = await request.post<CandidateAPIData>(url, params.body);

  return res;
};

export const deleteCandidateById = async (
  id: string
): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleCandidateById(id);
  const { data } = await request.delete<CandidateAPIData>(url);

  return data;
};

export const deleteCandidencyById = async (
  id: string
): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleCandidateById(id);
  const { data } = await request.delete<CandidateAPIData>(url);

  return data;
};

export const addCandidateExperience = async (params: {
  candidateId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleExperienceById(params.candidateId);
  const { data } = await request.post<CandidateAPIData>(url, params.body);

  return data;
};

export const editCandidateExperience = async (params: {
  experienceId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleExperienceById(params.experienceId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);

  return data;
};

export const removeCandidateExperience = async (params: {
  experienceId: string;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleExperienceById(params.experienceId);
  const { data } = await request.delete<CandidateAPIData>(url);

  return data;
};

export const addCandidateEducation = async (params: {
  candidateId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleEducationById(params.candidateId);
  const { data } = await request.post<CandidateAPIData>(url, params.body);

  return data;
};

export const editCandidateEducation = async (params: {
  educationId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleEducationById(params.educationId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);

  return data;
};

export const removeCandidateEducation = async (params: {
  educationId: string;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleEducationById(params.educationId);
  const { data } = await request.delete<CandidateAPIData>(url);

  return data;
};

export const addCandidateSkill = async (params: {
  candidateId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleSkillById(params.candidateId);
  const { data } = await request.post<CandidateAPIData>(url, params.body);

  return data;
};

export const editCandidateSkill = async (params: {
  skillId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleSkillById(params.skillId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);

  return data;
};

export const removeCandidateSkill = async (params: {
  skillId: string;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleSkillById(params.skillId);
  const { data } = await request.delete<CandidateAPIData>(url);

  return data;
};

export const addCandidateLanguage = async (params: {
  candidateId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleLanguageById(params.candidateId);
  const { data } = await request.post<CandidateAPIData>(url, params.body);

  return data;
};

export const editCandidateLanguage = async (params: {
  languageId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleLanguageById(params.languageId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);

  return data;
};

export const removeCandidateLanguage = async (params: {
  languageId: string;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleEducationById(params.languageId);
  const { data } = await request.delete<CandidateAPIData>(url);

  return data;
};

export const linkJobsToCandidate = async ({
  candidateId,
  jobIds,
}: {
  candidateId: string;
  jobIds: string[];
}): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.candidacy, {
    candidateId,
    jobIds,
  });

  return data;
};

export const unlinkJobsFromCandidate = async (
  candidateId: string
): Promise<any> => {
  const { data } = await request.delete(
    jobsEndpoints.getSingleCandidacyById(candidateId)
  );

  return data;
};

interface SearchCandidateArgs {
  params: {
    text?: string;
    currentEntityId?: string | number;
    occupations?: string[];
    page?: number;
    pageSize?: number;
  };
}
export const searchCandidate = async (
  params: ISearchParams
): Promise<PaginateResponse<ICandidateListItemProps>> => {
  const { data } = await request.get<PaginateResponse<BECandidateSearchResult>>(
    candidateEndpoints.search,
    { params }
  );
  console.log({ data });

  return {
    ...data,
    content: data.content.map(candidateSearchItemNormalizer),
  };
};

export const searchCandidateParticipations = async ({
  params,
}: SearchCandidateArgs) => {
  const { data } = await request.get<PaginateResponse<BECandidateSearchResult>>(
    candidateEndpoints.search,
    { params }
  );

  return data;
};

export const getCandidateFilters = async ({
  params,
}: {
  params: any;
}): Promise<any> => {
  const { data } = await request.get(candidateEndpoints.filter, {
    params,
  });

  return searchFiltersNormalizer.candidateFilters(data);
};

export const searchCandidateActivities = async (params: any): Promise<any> => {
  const { data } = await request.get<PaginateResponse<ActivityProps>>(
    jobsEndpoints.searchActivities,
    { params }
  );

  return data;
};

export const searchCandidates = async (
  params: any
): Promise<PaginateResponse<any>> => {
  const { data } = await request.get<PaginateResponse<ProjectProps>>(
    candidateEndpoints.search,
    params
  );

  return data;
};

export const getUpcomingMeetings = async ({
  params,
}: {
  params: {
    candidate: string | number;
    page?: number;
    size?: number;
    isCandidateMode: boolean;
  };
}): Promise<MeetingDataProps> => {
  const { isCandidateMode, ...restParams } = params;

  const { data } = await request.get<MeetingDataProps>(
    isCandidateMode
      ? candidateEndpoints.upcomingMeetings
      : jobsEndpoints.upcomingMeetings,
    {
      params: restParams,
    }
  );

  return data;
};

export const getPastMeetings = async ({
  params,
}: {
  params: {
    candidate: string | number;
    page?: number;
    size?: number;
    isCandidateMode: boolean;
  };
}): Promise<MeetingDataProps> => {
  const { isCandidateMode, ...restParams } = params;

  const { data } = await request.get<MeetingDataProps>(
    isCandidateMode
      ? candidateEndpoints.pastMeetings
      : jobsEndpoints.pastMeetings,
    {
      params: restParams,
    }
  );

  return data;
};

export const sendCandidateInvitation = async (params: {
  body: SendCandidateInvitationRequestBody;
}): Promise<unknown> => {
  const url = invitationEndpoints.sendCandidateInvitation;
  const { data } = await request.post<unknown>(url, params.body);

  return data;
};

export const getCandidateSummary = async ({
  params,
}: {
  params: { id?: string };
}): Promise<ICandidateSummaryOptions> => {
  const { id, ...otherParams } = params;
  const { data } = await request.get<BECandidateSummary>(
    candidateEndpoints.getSummary(id),
    {
      params: otherParams,
    }
  );

  return normalizeCandidateSummary(data);
};

export const getCandidateCompare = async ({
  params = {},
}: {
  params: { id?: string };
}): Promise<BECandidateSearchResult[]> => {
  const { id } = params;
  const { data } = await request.get<BECandidateSearchResult[]>(
    candidateEndpoints.candidateCompare(params?.id)
  );

  return data;
};

export const updateBulkEmail = async (params: {
  templateId: number;
  candidateIds: number[];
}): Promise<any> => {
  const { data } = await request.post<any>(
    candidateEndpoints.updateBulkEmail,
    params
  );
  return data;
};

export const updateBulkTodo = async (params: {
  candidateIds: number[];
  title: string;
  description: string;
  assigneeUserId: number;
  allTeamMembersTagged: boolean;
  taggedUserIds: number[];
  start: string;
  end: string;
  remind: boolean;
}): Promise<any> => {
  const { data } = await request.post<any>(
    candidateEndpoints.updateBulkTodo,
    params
  );
  return data;
};

export const updateBulkNote = async (params: {
  candidateIds: number[];
  body: string;
  visibility: 'EVERYONE';
  fileIds: number[];
}): Promise<any> => {
  const { data } = await request.post<any>(
    candidateEndpoints.updateBulkNote,
    params
  );
  return data;
};

export const getCandidateLastView = async ({
  params,
}: {
  params: { id: string };
}): Promise<CandidateLastViewData> => {
  const url = candidateEndpoints.candidateLastView(params.id);
  const { data } = await request.get<Promise<CandidateLastViewData>>(url);

  return data;
};
export const addSavedFilter = async (body: any): Promise<any> => {
  const { data } = await request.post(candidateEndpoints.addSavedFilter, body);

  return data;
};

export const editSavedFilter = async (body: any): Promise<any> => {
  const { data } = await request.put(
    candidateEndpoints.editSavedFilterById(body?.id),
    body
  );

  return data;
};

export const removeSavedFilter = async (id: string): Promise<any> => {
  const { data } = await request.delete(
    candidateEndpoints.removeSavedFilterById(id)
  );

  return data;
};

export const getAllSavedFilters = async (): Promise<any> => {
  const { data } = await request.get(candidateEndpoints.getAllSavedFilters);

  return data?.content;
};

export const setLockMode = async (params: {
  candidateId: string;
  enabled: boolean;
}): Promise<any> => {
  const { data } = await request.put(
    candidateEndpoints.setLockMode(params.candidateId),
    {
      enabled: params.enabled,
    }
  );
  return data;
};
