import type {
  BECandidateTodo,
  CandidateTodoRequest,
  ICandidateTodo,
} from '@shared/types/candidates';
import type { PaginateResponse } from '@shared/types/response';
import type { TodoEntityType, TodoStatusType } from '@shared/types/todo';
import {
  candidateEndpoints,
  jobsEndpoints,
  schedulesSearchEndpoints,
} from '../../constants/servicesEndpoints';
import { normalizeCandidateTodo } from '../../normalizers/beforeCacheCandidateInfo';
import request from '../../toolkit/request';

export const getCandidatesPagedTodos = async (params: {
  candidateId?: string | number;
  projectId?: string | number;
  page?: number;
  size?: number;
  text?: string;
  status?: TodoStatusType;
  onlyDone?: boolean;
  assigneeUserIds?: number[];
  participationId?: string | number;
  entityType?: TodoEntityType;
  isCandidateMode: boolean;
}): Promise<PaginateResponse<ICandidateTodo>> => {
  const { isCandidateMode, ...resetParams } = params;
  const url = isCandidateMode ? candidateEndpoints.todos : jobsEndpoints.todos;

  const { data } = await request.get<PaginateResponse<BECandidateTodo>>(url, {
    params: resetParams,
  });

  return {
    ...data,
    content: data.content.map(normalizeCandidateTodo),
  };
};

export const addCandidateTodo = async (params: {
  id: string;
  isCandidateMode: boolean;
  body: CandidateTodoRequest;
}): Promise<BECandidateTodo> => {
  const { id, isCandidateMode, body } = params;

  const url = isCandidateMode
    ? candidateEndpoints.singleTodoById(id)
    : jobsEndpoints.addTodo(id);
  const { data } = await request.post<BECandidateTodo>(url, body);
  return data;
};

export const editCandidateTodo = async (
  todoId: string,
  body: CandidateTodoRequest,
  isCandidateMode: boolean
) => {
  const url = isCandidateMode
    ? candidateEndpoints.singleTodoById(todoId)
    : jobsEndpoints.singleTodoById(todoId);
  return request.put<BECandidateTodo>(url, body);
};

export const removeCandidateTodo = async (params: {
  todoId: string;
  isCandidateMode: boolean;
}): Promise<BECandidateTodo> => {
  const url = params?.isCandidateMode
    ? candidateEndpoints.singleTodoById(params.todoId)
    : jobsEndpoints.singleTodoById(params.todoId);
  candidateEndpoints.singleTodoById(params.todoId);
  const { data } = await request.delete<BECandidateTodo>(url);
  return data;
};
