import type {
  BECandidateMeeting,
  CandidateMeetingRequest,
  ICandidateMeeting,
} from '@shared/types/candidates';
import { normalizeCandidateMeeting } from '@shared/utils/normalizers/beforeCacheCandidateInfo';
import type { ICreateMeetingData } from '@shared/types/schedules/schedules';
import { candidateEndpoints, jobsEndpoints } from '../../constants/servicesEndpoints';
import request from '../../toolkit/request';

export const getCandidatesMeetings = async (params: {
  candidateId?: string | number;
  page?: number;
  size?: number;
  onlyDone?: boolean;
}): Promise<Array<ICandidateMeeting>> => {
  const { data } = await request.get<Array<BECandidateMeeting>>(
    candidateEndpoints.meetings,
    params
  );

  return data.map(normalizeCandidateMeeting);
};

export const addCandidateMeeting = async (
  candidateId: string,
  body: CandidateMeetingRequest
): Promise<BECandidateMeeting> => {
  const url = candidateEndpoints.singleMeetingById(candidateId);
  const { data } = await request.post<BECandidateMeeting>(url, body);
  return data;
};

export const editCandidateMeeting = async (
  meetingId: string,
  body: CandidateMeetingRequest
) => {
  const url = candidateEndpoints.singleMeetingById(meetingId);
  return request.put<BECandidateMeeting>(url, body);
};

export const removeCandidateMeeting = async (params: {
  meetingId: string;
}): Promise<BECandidateMeeting> => {
  const url = candidateEndpoints.singleMeetingById(params.meetingId);
  const { data } = await request.delete<BECandidateMeeting>(url);
  return data;
};
export const createCandidateMeeting = async (
  candidateId: string,
  meetingData: ICreateMeetingData,
  isCandidateMode: boolean
) => {
  const { data } = await request.post(
    isCandidateMode
      ? candidateEndpoints.createCandidateMeeting(candidateId)
      : jobsEndpoints.createCandidateMeeting(candidateId),
    meetingData
  );
  return data;
};
