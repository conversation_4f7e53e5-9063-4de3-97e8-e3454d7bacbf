import { appEnvironment } from './constants';

export function getEmbededDocumentLink(
  src: string,
  options?: { locale?: string }
) {
  const baseUrl = `${appEnvironment.googleStorageBaseUrl}/lobox_public_resumes`;
  const isInternalUrl = src.startsWith(baseUrl);
  const mirroredSrc = src.replace(baseUrl, '/lobox_public_resumes');
  const optionsParams = new URLSearchParams(options ?? {}).toString();
  const hash = optionsParams ? `#${optionsParams}` : '';
  const fullUrl =
    src.startsWith('http') || src.startsWith('blob')
      ? src
      : `${appEnvironment.baseUrl}${src}`;
  const viwer = `/pdf-viewer/web/viewer.html?file=`;

  return {
    mirroredSrc,
    embededSrc: `${appEnvironment.baseUrl}${viwer}${isInternalUrl ? mirroredSrc : fullUrl}${hash}`,
  };
}
