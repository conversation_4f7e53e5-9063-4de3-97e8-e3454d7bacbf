import axios from 'axios';
import eventKeys from '@shared/constants/event-keys';
import { MESSAGE_SERVICE } from '@shared/utils/constants/servicesEndpoints/services/message';
import { decodeObject } from '@shared/utils/toolkit/decodeObject';
import authEndpoints from 'shared/utils/constants/servicesEndpoints/services/auth';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import getBrowserLanguage from 'shared/utils/toolkit/getBrowserLanguage';
import Cookies from './cookies';
import event from './event';
import getCookieKey from './getCookieKey';
import isServer from './isServer';
import onSuccessLogout from './onSuccessLogout';
import removeEmptyFromObject from './removeEmptyFromObject';
import { apiUrlMaker } from './urlMaker';
import type {
  AxiosProgressEvent,
  AxiosRequestConfig,
  AxiosResponse,
} from 'axios';

export type RequestOptionType<P = object> = {
  params?: P;
  headers?: Record<string, string>;
  accessToken?: string;
  isWholePath?: boolean;
  timeout?: number;
  cancelToken?: any;
  signal?: AbortSignal;
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
};

const apiClient = axios.create();

// Add response interceptor to handle the planRestriction
apiClient.interceptors.response.use(
  (response) => {
    if (response.data && response.data.limitedInPlan) {
      console.log('response', response);
      if (!isServer()) {
        event.trigger(eventKeys.planRestrictionsApplied, {
          limitedInPlan: true,
          featureName: response.data.featureName,
        });
      }
    }

    return response;
  },
  (error) => Promise.reject(error)
);

let isRefreshing = false;
let refreshPromise: Promise<void> | null = null;

async function refreshAccessToken(
  objCookieKey: string,
  deviceId: string
): Promise<void> {
  if (isRefreshing && refreshPromise) {
    return refreshPromise;
  }

  isRefreshing = true;
  refreshPromise = (async () => {
    const tokenObj = JSON.parse(Cookies.get(objCookieKey) || '{}');
    const refreshToken = tokenObj?.refreshToken;

    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    let businessPage: any;
    if (isBusinessApp) {
      const BUSINESS_PAGE = getCookieKey('businessPage');
      businessPage = decodeObject(Cookies.get(BUSINESS_PAGE));
    }

    const refreshUrl = apiUrlMaker(authEndpoints.refreshToken, '', false);

    try {
      const response = await apiClient.post(refreshUrl, {
        deviceId,
        userId: tokenObj.userId,
        refresh: refreshToken,
        fromPageId: businessPage?.id,
      });

      const newTokenObj = response.data;
      Cookies.set(objCookieKey, JSON.stringify(newTokenObj));
    } finally {
      isRefreshing = false;
      refreshPromise = null;
    }
  })();

  return refreshPromise;
}

async function handleLogout() {
  console.error('Logging out user due to 403 or refresh failure');
  onSuccessLogout();
}

async function request<T = any, D = any, P = any>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  url: string,
  data?: D,
  options?: RequestOptionType<P>
): Promise<AxiosResponse<T>> {
  const isOnServer = isServer();
  const isMessageService = url.includes(MESSAGE_SERVICE);

  // Determine cookie keys
  const userObjKey = getCookieKey('userObjToken');
  const businessObjKey = getCookieKey('businessObjToken');
  const objCookieKey =
    isBusinessApp && !isMessageService ? businessObjKey : userObjKey;

  // Retrieve tokens
  let tokenObj = JSON.parse(Cookies.get(objCookieKey) || '{}');
  const accessToken = options?.accessToken || tokenObj?.accessToken;

  const deviceIdKey = getCookieKey('deviceId');
  const deviceId = Cookies.get(deviceIdKey);

  const isUploadFile =
    options?.headers?.['Content-Type'] === 'multipart/form-data';

  // Prepare headers
  const headers: Record<string, string> = {
    ...(isUploadFile
      ? {}
      : {
          'Content-Type': 'application/json',
          'Accept-Language': getBrowserLanguage(),
        }),
    ...options?.headers,
  };

  if (accessToken) {
    headers.Authorization = `Bearer ${accessToken}`;
  }

  // Handle URL and params
  const queryParams = removeEmptyFromObject(options?.params || {});
  const urlSearchParams = new URLSearchParams(
    queryParams as Record<string, string>
  );
  const URL = apiUrlMaker(
    url,
    urlSearchParams.toString(),
    options?.isWholePath
  );

  const axiosConfig: AxiosRequestConfig = {
    method,
    url: URL,
    headers,
    data: isUploadFile ? data : data ? JSON.stringify(data) : undefined,
    timeout: options?.timeout,
    signal: options?.signal,
    cancelToken: options?.cancelToken,
    onUploadProgress: options?.onUploadProgress,
  };

  try {
    const response = await apiClient<T>(axiosConfig);

    return response;
  } catch (error: any) {
    // Handle 429 error, dispatch planRestrictionsApplied event
    if (error.response?.status === 429 && !isOnServer) {
      event.trigger(eventKeys.planRestrictionsApplied, error.response?.data);
    }
    // If unauthorized, attempt token refresh
    if (error.response?.status === 401 && !isOnServer) {
      try {
        await refreshAccessToken(objCookieKey, deviceId || '');
        tokenObj = JSON.parse(Cookies.get(objCookieKey) || '{}');
        if (tokenObj.accessToken) {
          // Retry request with new token
          axiosConfig.headers = {
            ...headers,
            Authorization: `Bearer ${tokenObj.accessToken}`,
          } as any;
          const retryResponse = await apiClient<T>(axiosConfig);

          return retryResponse;
        }
      } catch (refreshError) {
        // Refresh failed, logout
        await handleLogout();
        throw refreshError;
      }
    }

    if (
      error.response?.status === 403 &&
      error.config?.url?.includes('refresh')
    ) {
      await handleLogout();
    }
    throw error;
  }
}

// Exported convenience methods
const http = {
  get: <R = any, P = any>(url: string, options?: RequestOptionType<P>) =>
    request<R, undefined, P>('GET', url, undefined, options),
  post: <R = any, D = any>(url: string, data: D, options?: RequestOptionType) =>
    request<R, D>('POST', url, data, options),
  put: <R = any, D = any>(url: string, data?: D, options?: RequestOptionType) =>
    request<R, D>('PUT', url, data, options),
  delete: <R = any, D = any>(
    url: string,
    data?: D,
    options?: RequestOptionType
  ) => request<R, D>('DELETE', url, data, options),
  upload: <R = any, D = any>(
    url: string,
    data: D,
    options?: RequestOptionType
  ) =>
    request<R, D>('POST', url, data, {
      ...options,
      headers: { ...options?.headers, 'Content-Type': 'multipart/form-data' },
    }),
};

export default http;
