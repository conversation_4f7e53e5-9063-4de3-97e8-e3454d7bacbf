import { NextResponse } from 'next/server';
import consoleLogger from '@shared/utils/consoleLogger';
import isRouterAccessibleByPortal from '@shared/utils/isRouterAccessibleByPortal';
import { decodeObject } from '@shared/utils/toolkit/decodeObject';
import { encodeObject } from '@shared/utils/toolkit/encodeObject';
import getCookieKey from '@shared/utils/toolkit/getCookieKey';
import { getClientAppSetting, refreshAccessToken } from 'shared/utils/api/auth';
import { getPageDetail } from 'shared/utils/api/page';
import { getAuthUser } from 'shared/utils/api/user';
import appEnvironment from 'shared/utils/constants/env';
import {
  doesNotNeedAuthPages,
  forbiddenForAuthUsersPages,
  needsAuthPages,
  routeNames,
  landingRouteNames,
} from 'shared/utils/constants/routeNames';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import type { NextRequest } from 'next/server';
import type { PageType } from 'shared/types/page';
import type { UserType } from 'shared/types/user';

const COOKIE_KEYS = {
  AUTH_USER: getCookieKey('authUser'),
  BUSINESS_AUTH_PAGE: getCookieKey('businessPage'),
  USER_OBJ_TOKEN: getCookieKey('userObjToken'),
  BUS_OBJ_TOKEN: getCookieKey('businessObjToken'),
  DEVICE_ID: getCookieKey('deviceId'),
  IS_DARK: getCookieKey('isDark'),
  LOCALE: getCookieKey('locale'),
  BUSINESS_ID: getCookieKey('businessId'),
};

const isNeedAuthPage = (pathname: string) =>
  needsAuthPages.some((x) =>
    pathname.split('/').includes(x.replace('/', ''))
  ) && !doesNotNeedAuthPages?.some?.((x: string) => pathname.includes(x));

const isForbiddenForAuthUsersPages = (pathname: string) =>
  forbiddenForAuthUsersPages.some((x) => x === pathname);

const businessPageNormalizer = (page: PageType) => {
  const myMemberships = page?.myMemberships?.reduce(
    (prev, curr) =>
      curr.status === 'ACCEPTED'
        ? [...prev, { status: curr.status, role: curr.role }]
        : prev,
    []
  );

  return {
    id: page?.id,
    croppedImageUrl: page?.croppedImageUrl,
    title: page?.title,
    username: page?.username,
    type: 'PAGE',
    status: page?.status,
    ownerId: page?.ownerId,
    myMemberships,
    isMember: !!myMemberships?.length,
    location: {
      countryCode: page?.location?.countryCode,
    },
  };
};

export const authUserNormalizer = (authUser: UserType) => ({
  id: authUser?.id,
  croppedImageUrl: authUser?.croppedImageUrl,
  fullName: authUser?.fullName,
  username: authUser?.username,
  type: 'PERSON',
  location: {
    countryCode: authUser?.location?.countryCode,
  },
});

export function appendAuthHeaders(
  request: NextRequest,
  response: NextResponse
) {
  // Set auth headers for client-side use
  const authUserCookie = decodeObject(
    request.cookies.get(COOKIE_KEYS.AUTH_USER)?.value || '{}'
  );

  if (Object.keys(authUserCookie || {}).length > 0) {
    response.headers.set('lbx-auth-user', encodeObject(authUserCookie));
  }

  // Business app headers
  const businessPageCookie = decodeObject(
    request.cookies.get(COOKIE_KEYS.BUSINESS_AUTH_PAGE)?.value || '{}'
  );

  if (Object.keys(businessPageCookie || {}).length > 0) {
    response.headers.set('lbx-business-page', encodeObject(businessPageCookie));
  }
}

export function isPrefetch(request: NextRequest) {
  const nextUrlHeader = request.headers.get('next-url');

  if (nextUrlHeader !== null) {
    return true;
  }

  return false;
}

export const handleRouterAccessibility = (
  pathname: string,
  requestUrl: string
): NextResponse | null =>
  !isRouterAccessibleByPortal(pathname)
    ? NextResponse.rewrite(new URL('/404', requestUrl))
    : null;

export const handleCalendarRedirect = (
  pathname: string,
  request: NextRequest
): NextResponse | null => {
  const { origin } = request.nextUrl;
  const calendarMonthUrl = new URL(routeNames.schedulesCalendarMonth, origin);

  return pathname === routeNames.schedulesCalendar
    ? NextResponse.redirect(new URL(calendarMonthUrl, request.url))
    : null;
};
export const handleAuthenticationRedirect = (
  pathname: string,
  request: NextRequest
): NextResponse | null => {
  const { origin } = request.nextUrl;
  const loginUrl = new URL(landingRouteNames.login, origin);

  const authUserCookie = decodeObject(
    request.cookies.get(COOKIE_KEYS.AUTH_USER)?.value || '{}'
  );

  const authUser =
    Object.keys(authUserCookie || {}).length > 0 ? authUserCookie : undefined;

  if (!authUser && isNeedAuthPage(pathname)) {
    return NextResponse.redirect(loginUrl);
  }

  return null;
};

export const handleAuthorizationRedirect = (
  pathname: string,
  request: NextRequest
): NextResponse | null => {
  const { origin } = request.nextUrl;
  const homeUrl = new URL(routeNames.home, origin);

  const authUserCookie = decodeObject(
    request.cookies.get(COOKIE_KEYS.AUTH_USER)?.value || '{}'
  );

  const authUser =
    Object.keys(authUserCookie || {}).length > 0 ? authUserCookie : undefined;

  if (authUser && isForbiddenForAuthUsersPages(pathname)) {
    return NextResponse.redirect(homeUrl);
  }

  return null;
};

const middlewareCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 30000; // 30 seconds

const getCachedData = (key: string) => {
  const cached = middlewareCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  middlewareCache.delete(key);
  return null;
};

const setCachedData = (key: string, data: any) => {
  middlewareCache.set(key, { data, timestamp: Date.now() });
};

/**
 * Only affect when (!authUser?.id && userObjToken?.accessToken)
 */
export const handleGetProfileAndRefreshToken = async (
  request: NextRequest,
  response: NextResponse
): Promise<NextResponse | null> => {
  const { pathname, origin } = request.nextUrl;
  const loginUrl = new URL(landingRouteNames.login, origin);

  const authUserCookie: any = decodeObject(
    request.cookies.get(COOKIE_KEYS.AUTH_USER)?.value || '{}'
  );

  const authUser =
    Object.keys(authUserCookie || {}).length > 0 ? authUserCookie : undefined;

  const userObjToken: any = decodeObject(
    request.cookies.get(COOKIE_KEYS.USER_OBJ_TOKEN)?.value || '{}'
  );

  const busObjToken: any = decodeObject(
    request.cookies.get(COOKIE_KEYS.BUS_OBJ_TOKEN)?.value || '{}'
  );

  if (!authUser?.id && userObjToken?.accessToken) {
    const cacheKey = `auth_user_${userObjToken.accessToken.slice(-10)}`;
    const cachedUser = getCachedData(cacheKey);

    if (cachedUser) {
      const normalizedAuthUser = authUserNormalizer(cachedUser);
      response.cookies.set(
        COOKIE_KEYS.AUTH_USER,
        encodeObject(normalizedAuthUser),
        { domain: appEnvironment.domain }
      );
      request.cookies.set(
        COOKIE_KEYS.AUTH_USER,
        encodeObject(normalizedAuthUser)
      );
      return null;
    }

    let newAuthUser;
    try {
      const authUserPromise = getAuthUser({
        accessToken: userObjToken.accessToken,
      });

      newAuthUser = await Promise.race([
        authUserPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Auth timeout')), 5000)
        )
      ]);

      setCachedData(cacheKey, newAuthUser);
    } catch (err) {
      const refreshResponse: any = await refreshAccessToken({
        refresh: isBusinessApp
          ? busObjToken?.refreshToken
          : userObjToken?.refreshToken,
        userId: userObjToken?.userId,
        deviceId: request.cookies.get(COOKIE_KEYS.DEVICE_ID),
      });

      if (!refreshResponse.ok) {
        console.log('#error_refresh_failed', { pathname });
        response.cookies.delete(COOKIE_KEYS.USER_OBJ_TOKEN);
        response.cookies.delete(COOKIE_KEYS.BUS_OBJ_TOKEN);
        response.cookies.delete(COOKIE_KEYS.AUTH_USER);
        return NextResponse.redirect(loginUrl);
      }

      const refreshData = await refreshResponse.json();
      response.cookies.set(
        isBusinessApp ? COOKIE_KEYS.BUS_OBJ_TOKEN : COOKIE_KEYS.USER_OBJ_TOKEN,
        JSON.stringify(refreshData),
        { domain: appEnvironment.domain }
      );
      try {
        const authUserPromise = getAuthUser({ accessToken: refreshData.accessToken });
        newAuthUser = await Promise.race([
          authUserPromise,
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Auth timeout after refresh')), 5000)
          )
        ]);

        setCachedData(cacheKey, newAuthUser);
      } catch (error) {
        response.cookies.delete(COOKIE_KEYS.USER_OBJ_TOKEN);
        response.cookies.delete(COOKIE_KEYS.BUS_OBJ_TOKEN);
        response.cookies.delete(COOKIE_KEYS.AUTH_USER);
        return NextResponse.redirect(loginUrl);
      }
    }

    if (newAuthUser) {
      // Move client app settings to async operation to avoid blocking
      if (!isBusinessApp && userObjToken?.accessToken) {
        // Load settings asynchronously without blocking navigation
        Promise.resolve().then(async () => {
          try {
            const userSettings = await getClientAppSetting({
              accessToken: userObjToken.accessToken,
            });
            const isDark = userSettings?.theme === 'dark';
            const locale = userSettings?.language;

            // These will be applied on next request
            response.cookies.set(COOKIE_KEYS.IS_DARK, String(isDark), {
              domain: appEnvironment.domain,
            });
            response.cookies.set(COOKIE_KEYS.LOCALE, locale, {
              domain: appEnvironment.domain,
            });
          } catch (error) {
            console.log('Failed to load user settings:', error);
          }
        });
      }

      const normalizedAuthUser = authUserNormalizer(newAuthUser);

      response.cookies.set(
        COOKIE_KEYS.AUTH_USER,
        encodeObject(normalizedAuthUser),
        {
          domain: appEnvironment.domain,
        }
      );
      request.cookies.set(
        COOKIE_KEYS.AUTH_USER,
        encodeObject(normalizedAuthUser)
      );
    }
  }

  return null;
};

export const handleBusinessPage = async (
  request: NextRequest,
  response: NextResponse
): Promise<NextResponse | null> => {
  const { origin } = request.nextUrl;
  const userObjToken: any = decodeObject(
    request.cookies.get(COOKIE_KEYS.USER_OBJ_TOKEN)?.value || '{}'
  );
  const businessId = request.cookies.get(COOKIE_KEYS.BUSINESS_ID)?.value;
  const homeUrl = new URL(routeNames.home, origin);

  if (isBusinessApp) {
    let businessPage: any;
    try {
      const businessPageCookie: any = decodeObject(
        request.cookies.get(COOKIE_KEYS.BUSINESS_AUTH_PAGE)?.value || '{}'
      );
      businessPage =
        Object.keys(businessPageCookie).length > 0
          ? businessPageCookie
          : undefined;

      if (!businessPage?.id && userObjToken?.accessToken && businessId) {
        consoleLogger.time(
          '#################### GET_PAGE_DETAIL_IN_MIDDLEWARE ######################'
        );

        const businessPageLocal = await getPageDetail({
          accessToken: userObjToken.accessToken,
          params: {
            username: businessId,
          },
        });

        consoleLogger.timeEnd(
          '#################### GET_PAGE_DETAIL_IN_MIDDLEWARE ######################'
        );
        businessPage = businessPageNormalizer(businessPageLocal);
        response.cookies.set(
          COOKIE_KEYS.BUSINESS_AUTH_PAGE,
          encodeObject(businessPage),
          {
            domain: appEnvironment.domain,
          }
        );
      }

      if (!businessPage?.isMember) {
        throw new Error('not a member');
      }
      response.headers.set('lbx-business-page', encodeObject(businessPage));
    } catch (e) {
      console.log('#error_businessPage', { e });
      response.cookies.delete(COOKIE_KEYS.BUSINESS_AUTH_PAGE);

      return NextResponse.redirect(homeUrl);
    }
  }

  return null;
};
