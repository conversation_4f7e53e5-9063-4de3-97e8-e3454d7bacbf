import { getPageDetail } from 'shared/utils/api/page';
import { QueryKeys } from 'shared/utils/constants/enums';
import Cookies from 'shared/utils/toolkit/cookies';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';
import isServer from 'shared/utils/toolkit/isServer';
import useReactQuery from './useReactQuery';
import type { BeforeCachePageDetailType } from '@shared/types/page';
import type { UseQueryResult } from '@tanstack/react-query';

export type UseBusinessPageProps = {
  isEnabled?: boolean;
};

export const getBusinessPageId = async () => {
  const BUSINESS_ID = getCookieKey('businessId');
  if (isServer()) {
    const serverCookies = (await import('next/headers')).cookies();

    return serverCookies.get(BUSINESS_ID)?.value;
  }

  return Cookies.get(BUSINESS_ID);
};

const useBusinessPage = ({
  isEnabled,
}: UseBusinessPageProps): UseQueryResult<BeforeCachePageDetailType> =>
  // @ts-ignore
  useReactQuery<BeforeCachePageDetailType, any>({
    action: {
      key: [QueryKeys.businessPage],
      apiFunc: async () => {
        const businessId = await getBusinessPageId();

        return getPageDetail({ params: { username: businessId } });
      },
    },
    config: {
      enabled: isEnabled,
      refetchOnMount: false,
    },
  });

export default useBusinessPage;
