import { type PlanTimeSpan } from '@shared/types/page';
import { getPlansList } from '../api/page';
import { QueryKeys } from '../constants';
import { type AppPortal, isBusinessApp } from '../getAppEnv';
import useReactQuery from './useReactQuery';

export function useGetPlansData({
  appPortal,
  timeSpan = 'MONTHLY',
}: {
  appPortal: AppPortal;
  timeSpan?: PlanTimeSpan;
}) {
  return useReactQuery({
    action: {
      key: [QueryKeys.getPlansList, appPortal, timeSpan],
      apiFunc: getPlansList,
      params: {
        portal: appPortal,
        timeSpan,
      },
      spreadParams: true,
    },
    config: {
      enabled: isBusinessApp,
    },
  });
}
