const NotificationVariants = {
  YOUR_POST_GOT_REACTION: 'YOUR_POST_GOT_REACTION',
  YOUR_POST_GOT_COMMENT: 'YOUR_POST_GOT_COMMENT',
  YOUR_COMMENT_GOT_REACTION: 'YOUR_COMMENT_GOT_REACTION',
  YOUR_COMMENT_GOT_REPLY: 'YOUR_COMMENT_GOT_REPLY',
  YOUR_POST_GET_SHARED: 'YOUR_POST_GET_SHARED',
  YOU_TAGGED_ON_POST: 'YOU_TAGGED_ON_POST',
  YOU_MENTIONED_ON_POST: 'YOU_MENTIONED_ON_POST',
  ADD_PROFILE_PHOTO: 'ADD_PROFILE_PHOTO',
  ADD_HEADER_IMAGE: 'ADD_HEADER_IMAGE',
  ADD_PROFILE_INFORMATION: 'ADD_PROFILE_INFORMATION',
  PRIVATE_USER_FOUND: 'PRIVATE_USER_FOUND',
  UNPARSED_RESUME_FOUND: 'UNPARSED_RESUME_FOUND',
  NEW_BIRTHDAY: 'NEW_BIRTHDAY',
  PEOPLE_YOU_MAY_KNOW: 'PEOPLE_YOU_MAY_KNOW',
  PAGES_YOU_MAY_KNOW: 'PAGES_YOU_MAY_KNOW',
  NEW_PERSON_FROM_YOUR_SCHOOL: 'NEW_PERSON_FROM_YOUR_SCHOOL',
  NEW_PERSON_FROM_YOUR_COMPANY: 'NEW_PERSON_FROM_YOUR_COMPANY',
  NEW_PAGE_ROLE_ASSIGNED: 'NEW_PAGE_ROLE_ASSIGNED',
  NEW_FOLLOWER: 'NEW_FOLLOWER',
  PASSWORD_CHANGED: 'PASSWORD_CHANGED',
  NEW_DEVICE_LOGGED_IN: 'NEW_DEVICE_LOGGED_IN',
  NEW_FOLLOW_REQUEST: 'NEW_FOLLOW_REQUEST',
  PUBLISHED_A_NEW_PAGE: 'PUBLISHED_A_NEW_PAGE',
  PAGE_ROLE_ACCEPTED: 'PAGE_ROLE_ACCEPTED',
  PAGE_ROLE_DECLINED: 'PAGE_ROLE_DECLINED',
  YOUR_FOLLOW_REQUEST_ACCEPTED: 'YOUR_FOLLOW_REQUEST_ACCEPTED',
  YOU_MENTIONED_ON_COMMENT: 'YOU_MENTIONED_ON_COMMENT',
  SAVED_BUT_NOT_APPLIED_JOB_FOUND: 'SAVED_BUT_NOT_APPLIED_JOB_FOUND',
  JOB_APPLICANT_COUNT_CHANGED: 'JOB_APPLICANT_COUNT_CHANGED',
  JOB_CANDIDATE_RECOMMENDATION_UPDATED: 'JOB_CANDIDATE_RECOMMENDATION_UPDATED',
  TOP_JOB_SUGGESTION_FOUND: 'TOP_JOB_SUGGESTION_FOUND',
  MEETING_CREATED: 'MEETING_CREATED',
  MEETING_UPDATED: 'MEETING_UPDATED',
  MEETING_REMOVED: 'MEETING_REMOVED',
  MEETING_ALARMED: 'MEETING_ALARMED',
  MEETING_SHARED: 'MEETING_SHARED',
  ATTENDEE_ADDED: 'ATTENDEE_ADDED',
  ATTENDEE_ACCEPTED: 'ATTENDEE_ACCEPTED',
  ATTENDEE_DECLINED: 'ATTENDEE_DECLINED',
  MEETING_BOOKED: 'MEETING_BOOKED',
  TASK_CREATED: 'TASK_CREATED',
  TASK_UPDATED: 'TASK_UPDATED',
  TASK_STATUS_CHANGED: 'TASK_STATUS_CHANGED',
  TASK_REMOVED: 'TASK_REMOVED',
  TASK_GOT_COMMENT: 'TASK_GOT_COMMENT',
  TASK_COMMENT_UPDATED: 'TASK_COMMENT_UPDATED',
  TASK_COMMENT_REMOVED: 'TASK_COMMENT_REMOVED',
  TASK_ASSIGNEE_DECLINED: 'TASK_ASSIGNEE_DECLINED',
  TASK_ASSIGNEE_ACCEPTED: 'TASK_ASSIGNEE_ACCEPTED',
  REMINDER_ALARMED: 'REMINDER_ALARMED',
  UN_SYNC_CALENDAR_DETECTED: 'UN_SYNC_CALENDAR_DETECTED',
  JOB_CLOSED: 'JOB_CLOSED',
  JOB_ALERT_MATCHED: 'JOB_ALERT_MATCHED',
  PEOPLE_INVITED: 'PEOPLE_INVITED',
  FAILURE_INVITED: 'FAILURE_INVITED',
  INSUFFICIENT_PROFILE_SKILL_FOUND: 'INSUFFICIENT_PROFILE_SKILL_FOUND',
  YOUR_CLOSEST_USER_COMMENT_GOT_REACTION:
    'YOUR_CLOSEST_USER_COMMENT_GOT_REACTION',
  YOUR_INVOLVED_COMMENT_GOT_REACTION: 'YOUR_INVOLVED_COMMENT_GOT_REACTION',
  INCOMPLETE_PROFILE_INFO_FOUND: 'INCOMPLETE_PROFILE_INFO_FOUND',
  INCOMPLETE_PROFILE_IMAGE_FOUND: 'INCOMPLETE_PROFILE_IMAGE_FOUND',
  INCOMPLETE_PAGE_CONTACT_INFO_FOUND: 'INCOMPLETE_PAGE_CONTACT_INFO_FOUND',
  INCOMPLETE_PAGE_IMAGE_FOUND: 'INCOMPLETE_PAGE_IMAGE_FOUND',
  YOUR_CLOSEST_USER_MENTIONED_IN_COMMENT_IN_OTHERS_POST:
    'YOUR_CLOSEST_USER_MENTIONED_IN_COMMENT_IN_OTHERS_POST',
  OTHERS_MENTIONED_IN_COMMENT_IN_YOUR_POST:
    'OTHERS_MENTIONED_IN_COMMENT_IN_YOUR_POST',
  YOU_MENTIONED_IN_COMMENT_IN_OTHERS_POST:
    'YOU_MENTIONED_IN_COMMENT_IN_OTHERS_POST',
  YOU_MENTIONED_IN_COMMENT_IN_YOUR_POST:
    'YOU_MENTIONED_IN_COMMENT_IN_YOUR_POST',
  YOUR_CLOSEST_USER_MENTIONED_IN_OTHERS_POST:
    'YOUR_CLOSEST_USER_MENTIONED_IN_OTHERS_POST',
  YOU_MENTIONED_IN_OTHERS_POST: 'YOU_MENTIONED_IN_OTHERS_POST',
  NEW_PORTAL_ACCESS_ASSIGNED: 'NEW_PORTAL_ACCESS_ASSIGNED',
  OTHERS_COMMENT_IN_YOU_POST_GOT_REPLY: 'OTHERS_COMMENT_IN_YOU_POST_GOT_REPLY',
  YOUR_COMMENT_IN_OTHERS_POST_GOT_REPLY:
    'YOUR_COMMENT_IN_OTHERS_POST_GOT_REPLY',
  YOUR_COMMENT_IN_YOUR_POST_GOT_REPLY: 'YOUR_COMMENT_IN_YOUR_POST_GOT_REPLY',
  YOUR_CLOSEST_USER_COMMENTED_ON_OTHERS_POST:
    'YOUR_CLOSEST_USER_COMMENTED_ON_OTHERS_POST',
  YOUR_COMMENT_IN_OTHERS_POST_GOT_REACTION:
    'YOUR_COMMENT_IN_OTHERS_POST_GOT_REACTION',
  OTHERS_COMMENT_IN_YOUR_POST_GOT_REACTION:
    'OTHERS_COMMENT_IN_YOUR_POST_GOT_REACTION',
  YOUR_COMMENT_IN_YOUR_POST_GOT_REACTION:
    'YOUR_COMMENT_IN_YOUR_POST_GOT_REACTION',
  YOUR_CLOSEST_USER_POST_GOT_REACTION: 'YOUR_CLOSEST_USER_POST_GOT_REACTION',
  YOUR_INVOLVED_POST_GOT_REACTION: 'YOUR_INVOLVED_POST_GOT_REACTION',
  OTHERS_COMMENT_THAT_YOU_MENTIONED_GOT_REPLY:
    'OTHERS_COMMENT_THAT_YOU_MENTIONED_GOT_REPLY',
  PLAN_ACTIVATED: 'PLAN_ACTIVATED',

  //* General
  general: {
    AVATAR: 'AVATAR',
    COVER: 'COVER',
    PROFILE: 'PROFILE',
    CREATE_PAGE: 'CREATE_PAGE',
  },
  INCOMPLETE_PAGE_HEADER_FOUND: 'INCOMPLETE_PAGE_HEADER_FOUND',
  INCOMPLETE_SIGNUP_FOUND: 'INCOMPLETE_SIGNUP_FOUND',
  USER_WITHOUT_ANY_PAGE_FOUND: 'USER_WITHOUT_ANY_PAGE_FOUND',
  USER_SAVED_JOB_CLOSED: 'USER_SAVED_JOB_CLOSED',
  FOLLOWER_COUNTER_MILESTONE_REACHED: 'FOLLOWER_COUNTER_MILESTONE_REACHED',
  BIRTHDAY_SHARED: 'BIRTHDAY_SHARED',
  HOLIDAY_SHARED: 'HOLIDAY_SHARED',
  JOB_SHARED: 'JOB_SHARED',
  PAGE_SHARED: 'PAGE_SHARED',
  PERSON_SHARED: 'PERSON_SHARED',
  POST_SHARED: 'POST_SHARED',
  RESUME_SHARED: 'RESUME_SHARED',
  TASK_SHARED: 'TASK_SHARED',
  SCHEDULED_POST: 'SCHEDULED_POST',
  NEW_MEMBER: 'NEW_MEMBER',
  REACH_AND_IMPRESSION: 'REACH_AND_IMPRESSION',
  POST_REQUEST: 'POST_REQUEST',
  POST_ALERT: 'POST_ALERT',
  MEMBER_MODIFICATION: 'MEMBER_MODIFICATION',
  CLUB_SHARE: 'CLUB_SHARE',
  CLUB_PERMISSION: 'CLUB_PERMISSION',
  POST_PUBLISHMENT: 'POST_PUBLISHMENT',
  CLUB_INVITATION: 'CLUB_INVITATION',
  FOLLOWING_POST_SHARE: 'FOLLOWING_POST_SHARE',
  PUBLISHMENT_STATUS: 'PUBLISHMENT_STATUS',
  NEW_ROLE: 'NEW_ROLE',
  OWNERSHIP_TRANSFER: 'OWNERSHIP_TRANSFER',
  PREMIUM_PLAN: 'PREMIUM_PLAN',
  UPDATE_AND_ENGAGEMENT: 'UPDATE_AND_ENGAGEMENT',
  PERFORMANCE_AND_INSIGHTS: 'PERFORMANCE_AND_INSIGHTS',
  ENGAGEMENT_TIPS: 'ENGAGEMENT_TIPS',
  PROMOTIONAL_TOOLS: 'PROMOTIONAL_TOOLS',
  EVENT_PROMOTION: 'EVENT_PROMOTION',
  MILESTONE_ACHIEVEMENT: 'MILESTONE_ACHIEVEMENT',
  PROFILE_SHOW_UP: 'PROFILE_SHOW_UP',
  NEW_PEOPLE_FROM_COMPANY: 'NEW_PEOPLE_FROM_COMPANY',
  NEW_PEOPLE_FROM_SCHOOL: 'NEW_PEOPLE_FROM_SCHOOL',
  FOLLOWING_PEOPLE_BIRTHDAY: 'FOLLOWING_PEOPLE_BIRTHDAY',
  INVITE_PEOPLE: 'INVITE_PEOPLE',
  POST_UPDATE: 'POST_UPDATE',
  WEBINAR: 'WEBINAR',
  SCHEDULED_ARTICLES: 'SCHEDULED_ARTICLES',
  ARTICLE_PUBLISHMENT: 'ARTICLE_PUBLISHMENT',
  ARTICLE_COMMENT: 'ARTICLE_COMMENT',
  ARTICLE_REACTION: 'ARTICLE_REACTION',
  ARTICLE_INSIGHTS: 'ARTICLE_INSIGHTS',
  ARTICLE_SHARE: 'ARTICLE_SHARE',
  ARTICLE_REPORT: 'ARTICLE_REPORT',
  ARTICLE_STATUS: 'ARTICLE_STATUS',
  COMMENT_REPORT: 'COMMENT_REPORT',
  SCHEDULED_NEWS: 'SCHEDULED_NEWS',
  NEWS_PUBLISHMENT: 'NEWS_PUBLISHMENT',
  NEWS_COMMENT: 'NEWS_COMMENT',
  NEWS_REACTION: 'NEWS_REACTION',
  NEWS_INSIGHTS: 'NEWS_INSIGHTS',
  NEWS_SHARE: 'NEWS_SHARE',
  NEWS_REPORT: 'NEWS_REPORT',
  NEWS_STATUS: 'NEWS_STATUS',
  PLAN_CANCELED: 'PLAN_CANCELED',
  PLAN_RENEWED: 'PLAN_RENEWED',
  PLAN_DOWNGRADED: 'PLAN_DOWNGRADED',
  CANDIDATE_JOINED: 'CANDIDATE_JOINED',
  // recruiter
  CLIENT_RELATIONSHIP_REQUESTED: 'CLIENT_RELATIONSHIP_REQUESTED',
  VENDOR_RELATIONSHIP_REQUESTED: 'VENDOR_RELATIONSHIP_REQUESTED',
} as const;

export const ToggleNotificationList = [
  'YOUR_POST_GOT_REACTION',
  'YOUR_POST_GOT_COMMENT',
  'YOUR_COMMENT_GOT_REACTION',
  'YOUR_COMMENT_GOT_REPLY',
  'YOUR_POST_GET_SHARED',
  'YOU_TAGGED_ON_POST',
  'YOU_MENTIONED_ON_POST',
  'NEW_BIRTHDAY',
  'PERSON_YOU_MAY_KNOW',
  'PAGES_YOU_MAY_KNOW',
  'NEW_PERSON_FROM_YOUR_SCHOOL',
  'NEW_PERSON_FROM_YOUR_COMPANY',
  'NEW_FOLLOWER',
  'NEW_FOLLOW_REQUEST',
  'YOUR_FOLLOW_REQUEST_ACCEPTED',
  'NEW_PAGE_ROLE_ASSIGNED',
  'YOU_MENTIONED_ON_COMMENT',
];

export default NotificationVariants;
