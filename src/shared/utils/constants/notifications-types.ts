import { NotificationModules } from '@shared/types/notification';
import NotificationVariants from './NotificationVariants';

const NotificationsTypes = {
  [NotificationModules.GENERAL]: {
    AVATAR: 'AVATAR',
    COVER: 'COVER',
    PROFILE_COMPLETION: 'PROFILE',
    CREATE_PAGE: 'CREATE_PAGE',
    [NotificationVariants.UNPARSED_RESUME_FOUND]:
      NotificationVariants.UNPARSED_RESUME_FOUND,
    [NotificationVariants.PRIVATE_USER_FOUND]:
      NotificationVariants.PRIVATE_USER_FOUND,
    [NotificationVariants.PLAN_ACTIVATED]: NotificationVariants.PLAN_ACTIVATED,
    [NotificationVariants.PLAN_RENEWED]: NotificationVariants.PLAN_RENEWED,
    [NotificationVariants.PLAN_DOWNGRADED]:
      NotificationVariants.PLAN_DOWNGRADED,
    [NotificationVariants.PLAN_CANCELED]: NotificationVariants.PLAN_CANCELED,
  },
  [NotificationModules.POST]: {
    POST_REACTION: 'POST_REACTION',
    POST_COMMENT: 'POST_COMMENT',
    COMMENT_REACTION: 'COMMENT_REACTION',
    COMMENT_REPLY: 'COMMENT_REPLY',
    COMMENT_MENTION: 'COMMENT_MENTION',
    POST_MENTION: 'POST_MENTION',
    POST_TAG: 'POST_TAG',
    POST_SHARE: 'POST_SHARE',
  },
  [NotificationModules.JOB]: {
    JOB_APPLICATION_STATUS: 'JOB_APPLICATION_STATUS',
    CLOSED_JOB: 'CLOSED_JOB',
    JOB_ALERT: 'JOB_ALERT',
    TOP_SUGGESTION_JOB: 'TOP_SUGGESTION_JOB',
    DAILY_JOB_RECOMMENDATION: 'DAILY_JOB_RECOMMENDATION',
    APPLICATION_REMINDER: 'APPLICATION_REMINDER',
    CANDIDATES: 'CANDIDATES',
    JOB_APPLICATION: 'JOB_APPLICATION',
  },
  [NotificationModules.SEARCH]: {
    [NotificationVariants.PROFILE_SHOW_UP]:
      NotificationVariants.PROFILE_SHOW_UP,
  },
  [NotificationModules.PEOPLE]: {
    [NotificationVariants.NEW_FOLLOWER]: NotificationVariants.NEW_FOLLOWER,
    [NotificationVariants.PEOPLE_YOU_MAY_KNOW]:
      NotificationVariants.PEOPLE_YOU_MAY_KNOW,
    [NotificationVariants.NEW_PEOPLE_FROM_COMPANY]:
      NotificationVariants.NEW_PEOPLE_FROM_COMPANY,
    [NotificationVariants.NEW_PEOPLE_FROM_SCHOOL]:
      NotificationVariants.NEW_PEOPLE_FROM_SCHOOL,
    [NotificationVariants.FOLLOWING_PEOPLE_BIRTHDAY]:
      NotificationVariants.FOLLOWING_PEOPLE_BIRTHDAY,
    [NotificationVariants.INVITE_PEOPLE]: NotificationVariants.INVITE_PEOPLE,
  },
  [NotificationModules.PAGE]: {
    [NotificationVariants.NEW_FOLLOWER]: NotificationVariants.NEW_FOLLOWER,
    [NotificationVariants.POST_UPDATE]: NotificationVariants.POST_UPDATE,
    [NotificationVariants.WEBINAR]: NotificationVariants.WEBINAR,
    [NotificationVariants.PAGES_YOU_MAY_KNOW]:
      NotificationVariants.PAGES_YOU_MAY_KNOW,
    [NotificationVariants.PUBLISHMENT_STATUS]:
      NotificationVariants.PUBLISHMENT_STATUS,
    [NotificationVariants.NEW_ROLE]: NotificationVariants.NEW_ROLE,
    [NotificationVariants.OWNERSHIP_TRANSFER]:
      NotificationVariants.OWNERSHIP_TRANSFER,
    [NotificationVariants.PREMIUM_PLAN]: NotificationVariants.PREMIUM_PLAN,
    [NotificationVariants.UPDATE_AND_ENGAGEMENT]:
      NotificationVariants.UPDATE_AND_ENGAGEMENT,
    [NotificationVariants.PERFORMANCE_AND_INSIGHTS]:
      NotificationVariants.PERFORMANCE_AND_INSIGHTS,
    [NotificationVariants.ENGAGEMENT_TIPS]:
      NotificationVariants.ENGAGEMENT_TIPS,
    [NotificationVariants.PROMOTIONAL_TOOLS]:
      NotificationVariants.PROMOTIONAL_TOOLS,
    [NotificationVariants.EVENT_PROMOTION]:
      NotificationVariants.EVENT_PROMOTION,
    [NotificationVariants.MILESTONE_ACHIEVEMENT]:
      NotificationVariants.MILESTONE_ACHIEVEMENT,
  },
  [NotificationModules.CLUB]: {
    [NotificationVariants.SCHEDULED_POST]: NotificationVariants.SCHEDULED_POST,
    [NotificationVariants.NEW_MEMBER]: NotificationVariants.NEW_MEMBER,
    [NotificationVariants.REACH_AND_IMPRESSION]:
      NotificationVariants.REACH_AND_IMPRESSION,
    [NotificationVariants.POST_REQUEST]: NotificationVariants.POST_REQUEST,
    [NotificationVariants.POST_ALERT]: NotificationVariants.POST_ALERT,
    [NotificationVariants.MEMBER_MODIFICATION]:
      NotificationVariants.MEMBER_MODIFICATION,
    [NotificationVariants.CLUB_SHARE]: NotificationVariants.CLUB_SHARE,
    [NotificationVariants.CLUB_PERMISSION]:
      NotificationVariants.CLUB_PERMISSION,
    [NotificationVariants.POST_PUBLISHMENT]:
      NotificationVariants.POST_PUBLISHMENT,
    [NotificationVariants.CLUB_INVITATION]:
      NotificationVariants.CLUB_INVITATION,
    [NotificationVariants.FOLLOWING_POST_SHARE]:
      NotificationVariants.FOLLOWING_POST_SHARE,
  },
  [NotificationModules.SCHEDULE]: {
    [NotificationVariants.MEETING_CREATED]:
      NotificationVariants.MEETING_CREATED,
    [NotificationVariants.MEETING_UPDATED]:
      NotificationVariants.MEETING_UPDATED,
    [NotificationVariants.MEETING_REMOVED]:
      NotificationVariants.MEETING_REMOVED,
    [NotificationVariants.MEETING_ALARMED]:
      NotificationVariants.MEETING_ALARMED,
    [NotificationVariants.MEETING_SHARED]: NotificationVariants.MEETING_SHARED,
    [NotificationVariants.ATTENDEE_ADDED]: NotificationVariants.ATTENDEE_ADDED,
    [NotificationVariants.ATTENDEE_ACCEPTED]:
      NotificationVariants.ATTENDEE_ACCEPTED,
    [NotificationVariants.ATTENDEE_DECLINED]:
      NotificationVariants.ATTENDEE_DECLINED,
    [NotificationVariants.MEETING_BOOKED]: NotificationVariants.MEETING_BOOKED,
    [NotificationVariants.TASK_CREATED]: NotificationVariants.TASK_CREATED,
    [NotificationVariants.TASK_UPDATED]: NotificationVariants.TASK_UPDATED,
    [NotificationVariants.TASK_STATUS_CHANGED]:
      NotificationVariants.TASK_STATUS_CHANGED,
    [NotificationVariants.TASK_REMOVED]: NotificationVariants.TASK_REMOVED,
    [NotificationVariants.TASK_GOT_COMMENT]:
      NotificationVariants.TASK_GOT_COMMENT,
    [NotificationVariants.TASK_COMMENT_UPDATED]:
      NotificationVariants.TASK_COMMENT_UPDATED,
    [NotificationVariants.TASK_COMMENT_REMOVED]:
      NotificationVariants.TASK_COMMENT_REMOVED,
    [NotificationVariants.TASK_ASSIGNEE_DECLINED]:
      NotificationVariants.TASK_ASSIGNEE_DECLINED,
    [NotificationVariants.TASK_ASSIGNEE_ACCEPTED]:
      NotificationVariants.TASK_ASSIGNEE_ACCEPTED,
    [NotificationVariants.REMINDER_ALARMED]:
      NotificationVariants.REMINDER_ALARMED,
    [NotificationVariants.UN_SYNC_CALENDAR_DETECTED]:
      NotificationVariants.UN_SYNC_CALENDAR_DETECTED,
    [NotificationVariants.CLIENT_RELATIONSHIP_REQUESTED]:
      NotificationVariants.CLIENT_RELATIONSHIP_REQUESTED,
  },
  [NotificationModules.ARTICLE]: {
    [NotificationVariants.SCHEDULED_ARTICLES]:
      NotificationVariants.SCHEDULED_ARTICLES,
    [NotificationVariants.ARTICLE_PUBLISHMENT]:
      NotificationVariants.ARTICLE_PUBLISHMENT,
    [NotificationVariants.ARTICLE_COMMENT]:
      NotificationVariants.ARTICLE_COMMENT,
    [NotificationVariants.ARTICLE_REACTION]:
      NotificationVariants.ARTICLE_REACTION,
    [NotificationVariants.ARTICLE_INSIGHTS]:
      NotificationVariants.ARTICLE_INSIGHTS,
    [NotificationVariants.ARTICLE_SHARE]: NotificationVariants.ARTICLE_SHARE,
    [NotificationVariants.ARTICLE_REPORT]: NotificationVariants.ARTICLE_REPORT,
    [NotificationVariants.ARTICLE_STATUS]: NotificationVariants.ARTICLE_STATUS,
    [NotificationVariants.COMMENT_REPORT]: NotificationVariants.COMMENT_REPORT,
  },
  [NotificationModules.NEWS]: {
    [NotificationVariants.SCHEDULED_NEWS]: NotificationVariants.SCHEDULED_NEWS,
    [NotificationVariants.NEWS_PUBLISHMENT]:
      NotificationVariants.NEWS_PUBLISHMENT,
    [NotificationVariants.NEWS_COMMENT]: NotificationVariants.NEWS_COMMENT,
    [NotificationVariants.NEWS_REACTION]: NotificationVariants.NEWS_REACTION,
    [NotificationVariants.NEWS_INSIGHTS]: NotificationVariants.NEWS_INSIGHTS,
    [NotificationVariants.NEWS_SHARE]: NotificationVariants.NEWS_SHARE,
    [NotificationVariants.NEWS_REPORT]: NotificationVariants.NEWS_REPORT,
    [NotificationVariants.COMMENT_REPORT]: NotificationVariants.COMMENT_REPORT,
    [NotificationVariants.NEWS_STATUS]: NotificationVariants.NEWS_STATUS,
  },
} as const;

export default NotificationsTypes;
