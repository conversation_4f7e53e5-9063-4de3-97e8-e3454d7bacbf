import { useState, useRef } from 'react';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import jobsApi from '@shared/utils/api/jobs';
import { uploadResumeProfile } from 'shared/utils/api/resumeParser';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useGetResume from '@shared/hooks/useGetResume';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import Flex from '@shared/uikit/Flex';
import MenuItem from '@shared/uikit/MenuItem';
import Typography from '@shared/uikit/Typography';
import useStateCallback from '@shared/utils/hooks/useStateCallback';

interface MessageProps {
  onChange: (value: boolean) => void;
}

const Message: React.FC<MessageProps> = ({ onChange }) => {
  const [value, onSelect] = useStateCallback<boolean>(false);
  const { t } = useTranslation();

  const onClick = (val: boolean) => onSelect(val, () => onChange(val));

  return (
    <Flex>
      <Typography size={15} height={15}>
        {t('d_y_w_t_a_t_r_t_y_p_o_over')}
      </Typography>
      <MenuItem
        className="!bg-gray_5 !mt-16_20"
        title={t('overwrite')}
        subTitle={t('y_n_r_w_r_t_t_c_p')}
        withHover
        iconVariant="square"
        iconBoxSize={40}
        iconSize={25}
        iconColor="smoke_coal"
        iconName="replace"
        actionProps={{
          type: 'radio',
          checked: !value,
        }}
        onClick={() => onClick(false)}
      />
      <MenuItem
        title={t('append_t_existing')}
        subTitle={t('y_p_w_b_u_w_t_n_d_f_n_r')}
        withHover
        className="!bg-gray_5 !mt-8_12"
        iconVariant="square"
        iconBoxSize={40}
        iconSize={25}
        iconColor="smoke_coal"
        iconName="append"
        actionProps={{
          type: 'radio',
          checked: value,
        }}
        onClick={() => onClick(true)}
      />
    </Flex>
  );
};

interface UseUploadResumeAndAnalyzeReturn {
  uploadResumeAndAnalyze: (
    originalFile: Blob,
    options: { onSuccess: () => void; onError: (error: Error) => void }
  ) => void;
  cancelUpload: () => void;
  isUploading: boolean;
  progress: number;
}

const useUploadResumeAndAnalyze = (): UseUploadResumeAndAnalyzeReturn => {
  const { t } = useTranslation();
  const { authUser } = useGetAppObject();
  const { data, refetch } = useGetResume({ userId: authUser?.id });
  const [isUploading, toggleIsUploading] = useState(false);
  const valueRef = useRef<boolean>(false);
  const abortControllerRef = useRef<
    [AbortController | null, AbortController | null]
  >([null, null]);
  abortControllerRef.current[0] = new AbortController();
  abortControllerRef.current[1] = new AbortController();

  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });
  const { mutate: analyzeResume } = useReactMutation({
    apiFunc: uploadResumeProfile,
  });

  const { mutate: uploadResume } = useReactMutation({
    apiFunc: jobsApi.uploadFile,
    onSettled: refetch,
  });
  const cancelUpload = () => {
    abortControllerRef.current?.forEach((item) => item?.abort());
    toggleIsUploading(false);
  };
  const uploadResumeAndAnalyze = (
    originalFile: Blob,
    {
      onSuccess,
      onError,
    }: { onSuccess: () => void; onError: (error: Error) => void }
  ) => {
    const confirmCallback = () => {
      toggleIsUploading(true);
      uploadResume({
        file: originalFile,
        signal: abortControllerRef.current[0]?.signal,
      });
      analyzeResume(
        {
          file: originalFile,
          append: valueRef.current,
          signal: abortControllerRef.current[1]?.signal,
        },
        {
          onSuccess,
          onError,
          onSettled: () => {
            toggleIsUploading(false);
          },
        }
      );
    };

    const onChange = (val: boolean) => (valueRef.current = val);

    return data?.url
      ? openConfirmDialog({
          title: t('update_profile_options'),
          message: <Message onChange={onChange} />,
          confirmButtonText: t('update'),
          cancelButtonText: t('skip'),
          confirmCallback,
        })
      : confirmCallback();
  };

  return { uploadResumeAndAnalyze, cancelUpload, isUploading, progress: 0 };
};

export default useUploadResumeAndAnalyze;
