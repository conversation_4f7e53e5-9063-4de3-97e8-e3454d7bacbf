import { getPageMembers } from 'shared/utils/api/page';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import type { UseQueryResult } from '@tanstack/react-query';
import type { PageMemberStatusType, RoleType } from '@shared/types/page';

export type UseGetPageMembersProps = {
  enabled?: boolean;
  businessPageId?: string;
  onSuccess?: ((data: PageMemberType[]) => void) | undefined;
};

export type PageMemberType = {
  id: string;
  role: RoleType;
  user: {
    id: string;
    fullName: string;
    username: string;
    image: string;
    job: string;
    croppedImageUrl?: string;
    occupation?: {
      label: string;
    };
  };
  userId?: string;
  pageId: string;
  status: PageMemberStatusType;
  portalAccesses: { portal: string; role: string }[];
};

export type UseGetPageMembersType = UseQueryResult<
  Array<PageMemberType>,
  any
> & {
  getPageMembersKey: Array<string>;
};

const useGetPageMembers = ({
  enabled,
  businessPageId,
  onSuccess,
}: UseGetPageMembersProps): UseGetPageMembersType => {
  const { businessPage } = useGetAppObject();
  const pageId = businessPageId || businessPage?.id;
  const getPageMembersKey = [QueryKeys.pageMembers, pageId] as string[];

  const queryResult = useReactQuery<Array<PageMemberType>>({
    action: {
      apiFunc: getPageMembers,
      key: getPageMembersKey,
      params: { pageId },
    },
    config: {
      enabled,
      onSuccess,
    },
  });

  return {
    ...queryResult,
    getPageMembersKey,
  };
};

export default useGetPageMembers;
