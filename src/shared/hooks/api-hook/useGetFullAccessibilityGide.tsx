import { decodeObject } from '@shared/utils/toolkit/decodeObject';
import { getFullAccessibilityGuide } from 'shared/utils/api/page';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import Cookies from 'shared/utils/toolkit/cookies';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';

const useGetFullAccessibilityGide = () => {
  const USER_OBJ_TOKEN = getCookieKey('userObjToken');
  const userObjToken = decodeObject(Cookies.get(USER_OBJ_TOKEN));
  const accessToken = userObjToken?.accessToken;

  return useReactQuery({
    action: {
      apiFunc: () => getFullAccessibilityGuide({ accessToken }),
      key: [QueryKeys.getFullAccessibilityGuide, accessToken],
    },
    config: {
      enabled: !!accessToken,
      staleTime: 24 * 60 * 60 * 1000,
      cacheTime: 24 * 60 * 60 * 1000,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchInterval: false,
    },
  });
};

export default useGetFullAccessibilityGide;
