import { useSearchParams } from 'next/navigation';
import { useGetLocationValueInFilter } from '@shared/hooks/useGetLocationValueInFilter';
import { jobsDb } from '@shared/utils/constants/enums';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import lookupNormalizer from '@shared/utils/normalizers/lookup';
import collectionToObjectByKey from '@shared/utils/toolkit/collectionToObjectByKey';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import geoApi from 'shared/utils/api/geo';
import { suggestObjects } from 'shared/utils/api/search';
import Endpoints from 'shared/utils/constants/endpoints';
import { searchEndPoints } from 'shared/utils/constants/servicesEndpoints';
import lookup from 'shared/utils/constants/servicesEndpoints/services/lookup';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { hashtagNormalizer } from 'shared/utils/normalizers/hashtagNormalizer';
import hereApiResponseNormalizer from 'shared/utils/normalizers/hereApiResponseNormalizer';
import lookupResponseNormalizer from 'shared/utils/normalizers/lookupResponseNormalizer';
import skillsResponseNormalizer from 'shared/utils/normalizers/skillsResponseNormalizer';
import { useAuthCountry } from '../useAuthCountry';
import useDynamicFilters from '../useDynamicFilters';
import { useCommonFilterGroups } from './useCommonFilterGroups';
import classes from './useSearchFiltersFields.module.scss';

const { jobSearchOptions } = jobsDb;
const searchEntitiesObject = collectionToObjectByKey(
  jobSearchOptions.searchEntities
);

export const useJobsFilterGroups = () => {
  const searchEntity = 'jobs';
  const { t } = useTranslation();
  const authCountry = useAuthCountry();
  const searchParams = useSearchParams();
  const getQueryValue = useGetNormalizedArrayQuery();
  const locationValueInFilter = useGetLocationValueInFilter();
  const { PAGE_NUMBER, QUERY, SORT_BY, DATE_POSTED } = useCommonFilterGroups();

  const searchGroupType =
    searchParams.get(searchFilterQueryParams.searchGroupType) ||
    searchGroupTypes.ALL;
  const isAllFilter = searchGroupType === searchGroupTypes.ALL;
  const isSavedFilter =
    searchParams.get(searchFilterQueryParams.searchGroupType) ===
    searchGroupTypes.SAVED;

  const countryCode = searchParams.get(searchFilterQueryParams.countryCode);

  const dynamic = useDynamicFilters();
  const categories = dynamic?.categories || [];
  const employmentTypes = dynamic?.employmentTypes || [];
  const workPlaceTypes = dynamic?.workPlaceTypes || [];
  const jobTitles = dynamic?.titles || [];
  const cities = dynamic?.cities || [];
  const pages = dynamic?.pages || [];
  const salaryRange = dynamic?.salaryRange || [];
  const skills = dynamic?.skills || [];
  const languages = dynamic?.languages || [];
  const benefits = dynamic?.benefits || [];
  const hashtags = dynamic?.hashtags || [];
  const creators = dynamic?.creators || [];

  const SEARCH_ENTITY = {
    name: searchFilterQueryParams.searchEntity,
    cp: 'list',
    options: jobSearchOptions.searchEntities,
    hiddenInHeader: false,
    alwaysShowInHeader: true,
    hiddenInForm: true,
    getValue: () => searchEntity,
    label: searchEntitiesObject[searchEntity]?.label,
    divider: { className: classes.groupDivider },
  };
  const PLACE_TITLE = {
    name: searchFilterQueryParams.locationTitle,
    cp: 'input',
    hiddenInHeader: true,
    hiddenInForm: true,
    getDefaultValue: () => locationValueInFilter.placeTitleValue,
    getValue: () => locationValueInFilter.placeTitleDefault,
    divider: { className: classes.groupDivider },
  };

  const PLACE_ID = {
    name: searchFilterQueryParams.countryCode,
    cp: 'input',
    hiddenInHeader: true,
    hiddenInForm: true,
    getDefaultValue: () => locationValueInFilter.countryCodeDefault,
    getValue: () => locationValueInFilter.countryCodeValue,
    divider: { className: classes.groupDivider },
  };

  const COUNTRYID = {
    name: searchFilterQueryParams.countryId,
    cp: 'input',
    hiddenInHeader: true,
    hiddenInForm: true,
    getValue: () => authCountry?.title?.id,
    divider: { className: classes.groupDivider },
  };

  const SEARCH_GROUP_TYPE_USER = {
    name: searchFilterQueryParams.searchGroupType,
    cp: 'radioGroup',
    options: jobSearchOptions.searchGroupType,
    hiddenInHeader: true,
    hiddenInForm: true,
    getValue: () => searchGroupType,
    divider: { className: classes.groupDivider },
  };

  const JOB_GROUP_STATUS = {
    name: searchFilterQueryParams.jobGroupStatus,
    cp: 'radioGroup',
    options: isSavedFilter
      ? jobSearchOptions.savedJobsGroupStatus
      : jobSearchOptions.appliedJobGroupStatus,
    hiddenInHeader: true,
    hiddenInForm: true,
    label: t('status'),
    getValue: () => getQueryValue(searchFilterQueryParams.jobGroupStatus),
    divider: { className: classes.groupDivider },
  };

  const CATEGORY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('category'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.categoryIds,
    divider: { className: classes.groupDivider },
    options: categories,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.getIndustry,
      normalizer: lookupResponseNormalizer,
    },
    label: t('category'),
    placeholder: t('search_category'),
    getValue: () => getQueryValue(searchFilterQueryParams.categoryIds, 'array'),
    alwaysShowInHeader: true,
  };

  const EXPERIENCE_LEVEL = {
    formGroup: {
      color: 'smoke_coal',
      title: t('exp_level'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.experienceLevels,
    divider: { className: classes.groupDivider },
    options: dynamic?.experienceLevels || [],
    label: t('exp_level'),
    placeholder: t('search_exp_level'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.experienceLevels, 'array'),
    alwaysShowInHeader: true,
  };

  const EMPLOYMENT_TYPE = {
    formGroup: {
      color: 'smoke_coal',
      title: t('j_type'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.employmentTypes,
    divider: { className: classes.groupDivider },
    options: employmentTypes,
    label: t('j_type'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.employmentTypes, 'array'),
    alwaysShowInHeader: true,
  };

  const JOB_MODEL = {
    formGroup: {
      color: 'smoke_coal',
      title: t('job_model'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.workPlaceTypes,
    divider: { className: classes.groupDivider },
    options: workPlaceTypes,
    label: t('job_model'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.workPlaceTypes, 'array'),
    alwaysShowInHeader: true,
  };

  const TITLES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('j_title'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.titles,
    divider: { className: classes.groupDivider },
    options: jobTitles,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.getOccupations,
      normalizer: lookupResponseNormalizer,
    },
    label: t('j_title'),
    placeholder: t('search_j_title'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.titles, 'array'),
    alwaysShowInHeader: true,
  };

  // --- location & related pages ---
  const LOCATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('location'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.cities,
    options: cities,
    label: t('locations'),
    placeholder: t('search_location'),
    getValue: () => getQueryValue(searchFilterQueryParams.cities, 'array'),
    alwaysShowInHeader: false,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      apiFunc: geoApi.suggestCity,
      params: { countryCode },
      normalizer: (items: any) =>
        items?.map((item: any) => ({
          label: item?.label,
          value: `${item?.label}::${item?.cityCode}`,
        })),
    },
  };

  const PAGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('page'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.pageIds,
    divider: { className: classes.groupDivider },
    options: pages,
    label: t('page'),
    placeholder: t('search_company'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
    },
    params: { countryCode },
    hiddenInBusiness: true,
    getValue: () => getQueryValue(searchFilterQueryParams.pageIds, 'array'),
  };

  const SALARY = !!salaryRange && {
    formGroup: {
      color: 'smoke_coal',
      title: t('salary_range'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    cp: 'salaryPicker',
    withConfirmation: false,
    name: searchFilterQueryParams.salaryRange,
    data: salaryRange,
    getValue: () => getQueryValue(searchFilterQueryParams.salaryRange),
    label: t('salary_range'),
  };

  const SKILLS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('skills'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    name: searchFilterQueryParams.skills,
    options: skills,
    label: t('skills'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.getSkills,
      normalizer: skillsResponseNormalizer,
      plusButtonClassName: classes.plusButtonClassName,
    },
    placeholder: t('search_skill'),
    getValue: () => getQueryValue(searchFilterQueryParams.skills, 'array'),
    divider: { className: classes.groupDivider },
    hiddenInHeader: !getQueryValue(searchFilterQueryParams.skills, 'array')
      ?.length,
  };

  const LANGUAGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('languages'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    name: searchFilterQueryParams.languageIds,
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    label: t('languages'),
    placeholder: t('search_language'),
    options: languages,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.getLanguages,
      normalizer: lookupResponseNormalizer,
    },
    getValue: () => getQueryValue(searchFilterQueryParams.languageIds, 'array'),
    alwaysShowInHeader: false,
  };

  const BENEFITS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('benefits'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { container: classes.benefitsContainer },
    name: searchFilterQueryParams.jobBenefitIds,
    options: benefits,
    label: t('benefits'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.jobBenefitIds, 'array'),
    divider: { className: classes.groupDivider },
    hiddenInHeader: !getQueryValue(
      searchFilterQueryParams.jobBenefitIds,
      'array'
    )?.length,
    placeholder: t('search_benefits'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: lookup.getBenefit,
      normalizer: lookupNormalizer.searchBenefits,
    },
  };

  const HASHTAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('hashtags'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.hashtags,
    options: hashtags,
    label: t('hashtags'),
    placeholder: t('search_hashtag'),
    getValue: () => getQueryValue(searchFilterQueryParams.hashtags, 'array'),
    alwaysShowInHeader: false,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: searchEndPoints.suggestHashtag,
      normalizer: hashtagNormalizer,
    },
  };

  const CREATED_BY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('created_by'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.creatorIds,
    divider: { className: classes.groupDivider },
    options: creators,
    label: t('creators'),
    placeholder: t('search_creator'),
    getValue: () => getQueryValue(searchFilterQueryParams.creatorIds, 'array'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      apiFunc: suggestObjects,
      normalizer: (response: any) =>
        response?.content?.map(
          ({ id, fullName, type, usernameAtSign }: any) => ({
            value: id,
            label: fullName,
            type,
            helperText: usernameAtSign,
          })
        ),
    },
  };

  const groups = [
    PAGE_NUMBER,
    QUERY,
    SEARCH_ENTITY,
    PLACE_TITLE,
    PLACE_ID,
    isAllFilter && COUNTRYID,
    SEARCH_GROUP_TYPE_USER,
    JOB_GROUP_STATUS,
    SORT_BY,
    DATE_POSTED,
    categories?.length && CATEGORY,
    EXPERIENCE_LEVEL,
    EMPLOYMENT_TYPE,
    JOB_MODEL,
    TITLES,
    LOCATION,
    PAGES,
    SALARY,
    SKILLS,
    LANGUAGES,
    BENEFITS,
    HASHTAGS,
  ];

  return groups.filter(Boolean);
};
