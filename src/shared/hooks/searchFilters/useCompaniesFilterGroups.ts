'use client';

import { useCommonFilterGroups } from '@shared/hooks/searchFilters/useCommonFilterGroups';
import useDynamicFilters from '@shared/hooks/useDynamicFilters';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import { hashtagNormalizer } from '@shared/utils/normalizers/hashtagNormalizer';
import { searchFilterQueryParams } from 'shared/constants/search';
import geoApi from 'shared/utils/api/geo';
import { Endpoints, searchEndPoints } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { makeCityValue } from 'shared/utils/makeCityValue';
import lookupResponseNormalizer from 'shared/utils/normalizers/lookupResponseNormalizer';
import classes from './useSearchFiltersFields.module.scss';

const FORM_GROUP_CLASS = '!py-0 !mb-12';
const PLUS_BUTTON_CLASS = '!mt-8 !ml-8';

export const useCompaniesFilterGroups = () => {
  const dynamicFilters = useDynamicFilters();
  const { t } = useTranslation();
  const getQueryValue = useGetNormalizedArrayQuery();
  const { SORT_BY } = useCommonFilterGroups();

  const ESTABLISHMENT_DATE = {
    formGroup: {
      color: 'smoke_coal',
      title: t('establishment_date'),
      className: FORM_GROUP_CLASS,
    },
    label: t('establishment_date'),
    cp: 'radioGroup',
    name: searchFilterQueryParams.establishmentDate,
    options: dynamicFilters.establishmentDate,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.establishmentDate) || 'ANY_TIME',
    isDefaultValue:
      !getQueryValue(searchFilterQueryParams.establishmentDate) ||
      getQueryValue(searchFilterQueryParams.establishmentDate) === 'ANY_TIME',
    divider: { className: classes.groupDivider },
  };

  const CATEGORY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('page_type'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.categoryIds,
    divider: { className: classes.groupDivider },
    options: dynamicFilters?.industries || [],
    label: t('page_type'),
    placeholder: t('search_category'),
    getValue: () => getQueryValue(searchFilterQueryParams.categoryIds, 'array'),
    alwaysShowInHeader: true,
  };

  const PAGE_TYPES = dynamicFilters.pageTypes?.length && {
    formGroup: {
      color: 'smoke_coal',
      title: t('page_type'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.categories,
    label: t('page_type'),
    options: dynamicFilters.categories,
    getValue: () => getQueryValue(searchFilterQueryParams.categories, 'array'),
    divider: { className: classes.groupDivider },
  };

  const LOCATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('location'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.cities,
    options: dynamicFilters.cities,
    label: t('locations'),
    placeholder: t('search_location'),
    getValue: () => getQueryValue(searchFilterQueryParams.cities, 'array'),
    asyncAutoCompleteProps: {
      plusButtonClassName: PLUS_BUTTON_CLASS,
      maxLength: 100,
      apiFunc: geoApi.suggestCity,
      normalizer: (items: any) =>
        items?.map((item: any) => ({
          label: item?.label,
          value: makeCityValue(item?.label, item?.cityCode),
        })),
    },
    divider: { className: classes.groupDivider },
  };

  const COMPANY_SIZE = dynamicFilters.companySizes?.length && {
    formGroup: {
      color: 'smoke_coal',
      title: t('page_size'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.companySizes,
    label: t('page_size'),
    options: dynamicFilters.companySizes,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.companySizes, 'array'),
    divider: { className: classes.groupDivider },
  };

  const HASHTAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('hashtags'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.hashtags,
    options: dynamicFilters.hashtags,
    label: t('hashtags'),
    placeholder: t('search_hashtag'),
    getValue: () => getQueryValue(searchFilterQueryParams.hashtags, 'array'),
    asyncAutoCompleteProps: {
      plusButtonClassName: PLUS_BUTTON_CLASS,
      maxLength: 100,
      url: searchEndPoints.suggestHashtag,
      normalizer: hashtagNormalizer,
    },
    divider: { className: classes.groupDivider },
  };
  const LANGUAGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('languages'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    name: searchFilterQueryParams.languageIds,
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    label: t('languages'),
    placeholder: t('search_language'),
    options: dynamicFilters.languages,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.getLanguages,
      normalizer: lookupResponseNormalizer,
    },
    getValue: () => getQueryValue(searchFilterQueryParams.languageIds, 'array'),
    alwaysShowInHeader: false,
  };

  const groups = [
    SORT_BY,
    ESTABLISHMENT_DATE,
    CATEGORY,
    PAGE_TYPES,
    LOCATION,
    COMPANY_SIZE,
    LANGUAGES,
    HASHTAGS,
  ];

  return groups.filter(Boolean);
};
