import dynamic from 'next/dynamic';
import React from 'react';
import GlobalSearchInputSkeleton from '@shared/components/molecules/GlobalSearchInput/GlobalsSearchInput.skeleton';
import useGetUnseenMessageCount from '@shared/hooks/api-hook/useUnseenMessageCountStore';
import useIsPagePublished from 'shared/hooks/useIsPagePublished';
import IconButton from 'shared/uikit/Button/IconButton';
import Flex from 'shared/uikit/Flex';
import Media from 'shared/uikit/Media';
import Create from './Create';
import MoreMenu from './MoreMenu';
import NotificationButton from './NotificationButton';
import classes from './TopBarItems.module.scss';

const ResponsiveGlobalSearch = dynamic(
  () => import('shared/components/molecules/ResponsiveGlobalSearch'),
  {
    loading: () => <GlobalSearchInputSkeleton />,
  }
);
interface Props extends React.PropsWithChildren {
  isSearchPageOnMobile: boolean;
  isSearchAllPage: boolean;
  handleMessagesClicked: () => void;
}

const TopBarItems: React.FC<Props> = ({
  isSearchPageOnMobile,
  isSearchAllPage,
  handleMessagesClicked,
}) => {
  const isPagePublished = useIsPagePublished();
  const { unseenMessageCount } = useGetUnseenMessageCount();

  return (
    <>
      {!isSearchPageOnMobile && (
        <Media lessThan="midDesktop">
          <IconButton
            badgeNumber={unseenMessageCount.LOBOX}
            onClick={handleMessagesClicked}
            name="comment-alt-lines-light"
            size="md20"
            type="far"
            colorSchema="secondary2"
          />
        </Media>
      )}
      {isPagePublished && (
        <>
          {!isSearchPageOnMobile && (
            <Flex className={classes.actionsWrapper}>
              <NotificationButton />
              <Create />
              <MoreMenu />
            </Flex>
          )}
          <ResponsiveGlobalSearch
            iconViewClassName={classes.marginRight}
            defaultView={isSearchAllPage ? 'input' : 'icon'}
            isFlexOne={false}
          />
        </>
      )}
    </>
  );
};

export default TopBarItems;
