import { useQueryClient } from '@tanstack/react-query';
import { useFormikContext } from 'formik';
import React, { useCallback, useMemo, useState } from 'react';
import MultipleInput from '@shared/components/molecules/MultipleInput';
import NoteItemSkeleton from '@shared/components/molecules/NoteItem/NoteItemSkeleton';
import SearchFilterIcon from '@shared/components/molecules/SearchFilterIcon';
import CandidateNoteCard from '@shared/components/Organism/CandidateNoteCard';
import { getNoteValidationSchema } from '@shared/components/Organism/CandidateNoteCard/CandidateNoteCard.component';
import { useNoteFields } from '@shared/components/Organism/CandidateNoteCard/hooks/useNoteFields';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import PlanRestrictionCard from '@shared/components/Organism/PlanRestrictionCard';
import { FeatureName } from '@shared/types/planRestriction';
import Flex from '@shared/uikit/Flex';
import Form from '@shared/uikit/Form';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import cnj from '@shared/uikit/utils/cnj';
import * as API from '@shared/utils/api/candidates/notes';
import { QueryKeys } from '@shared/utils/constants';
import { NOTE_VISIBILITY_VALUES } from '@shared/utils/constants/enums/candidateDb';
import { useDebounceState } from '@shared/utils/hooks/useDebounceState';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { FooterWrap } from '../../components/FooterWrap';
import { usePanelFilter } from '../../hooks/usePanelFilter';
import classes from '../tab.module.scss';
import type {
  CandidateFormData,
  CandidateNoteRequest,
  ICandidateNote,
} from '@shared/types/candidates';
import type { UpdateNoteFormFields } from '@shared/types/note';
import type { FormikProps } from 'formik';

interface CandidateNotesManagerrProps {
  candidate: CandidateFormData;
}

export function CandidateNotesManager({
  candidate,
}: CandidateNotesManagerrProps) {
  const [formKey, setFormKey] = useState(0);
  const { t } = useTranslation();
  const keyword = useDebounceState('', 500);
  const { filters, show: showFilters } = usePanelFilter('notes');
  const queryClient = useQueryClient();

  const noteFilters = {
    ...filters,
    visibility: filters.visibility === 'ALL' ? undefined : filters.visibility,
  };

  const { isLoading, data: notes } = useReactInfiniteQuery<ICandidateNote>(
    [QueryKeys.candidateNotes, candidate.id, filters, keyword.debounceValue],
    {
      func: API.getCandidatesNotes,
      extraProps: {
        candidateId: candidate.id,
        text: keyword.debounceValue,
        ...noteFilters,
      },
      size: 10,
      spreadParams: true,
    }
  );

  const onSuccess = useCallback(
    (
      apiResponse: any,
      values?: any,
      formikRef?: FormikProps<UpdateNoteFormFields>
    ) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.candidateNotes, candidate.id],
        exact: false,
      });

      formikRef?.resetForm();
      setFormKey((k) => k + 1);
    },
    [candidate.id, queryClient]
  );

  const apiFunc = useCallback(API.addCandidateNote.bind(null, candidate.id), [
    candidate.id,
  ]);

  const validationSchema = useMemo(getNoteValidationSchema, []);

  return (
    <>
      <Flex className={cnj('sticky top-0 w-full', classes.scrollArea)}>
        <Flex flexDir="row" className="gap-20">
          <SearchInputV2
            value={keyword.value}
            onChange={keyword.setValue}
            placeholder={t('search_notes')}
            className="flex-1"
          />
          <SearchFilterIcon
            size="xl40"
            variant="rectangle"
            name="sliders-simple-light"
            type="far"
            onClick={showFilters}
          />
        </Flex>
      </Flex>
      <Flex className={cnj('flex-1 !pt-0', classes.scrollArea)}>
        <PlanRestrictionCard featuresName={[FeatureName.SEARCH_COMPANIES]} />
        {isLoading ? (
          <NoteItemSkeleton
            classNames={{
              root: 'border border-solid !border-techGray_20 bg-background',
              container: '!p-12',
            }}
          />
        ) : notes?.length ? (
          notes.map((note) => <CandidateNoteCard key={note.id} item={note} />)
        ) : (
          <EmptySearchResult
            className="h-full w-full items-center"
            title={t('no_notes_found')}
            sectionMessage={t('no_notes_found_desc')}
          />
        )}
      </Flex>
      <Form
        key={formKey}
        initialValues={freshNote}
        apiFunc={apiFunc}
        onSuccess={onSuccess}
        transform={newCandidateNoteTransform}
        validationSchema={validationSchema}
      >
        <FooterWrap>
          <NoteMultipleInput />
        </FooterWrap>
      </Form>
    </>
  );
}

function NoteMultipleInput() {
  const fields = useNoteFields();

  const { values } = useFormikContext<UpdateNoteFormFields>();

  const groups = useMemo(
    () =>
      [
        fields.BODY_COMPOSE,
        fields.VISIBILITY,
        values.fileIds?.length ? fields.ATTACHMENT_LIST : undefined,
        fields.ATTACHMENT_ICON,
      ].filter(Boolean),
    [fields, values]
  );
  const expand = !!values.body && values.body !== '<p></p>';

  return <MultipleInput showAuthorAvatar groups={groups} expand={expand} />;
}

const freshNote: UpdateNoteFormFields = {
  body: '<p></p>',
  visibility: NOTE_VISIBILITY_VALUES.ONLY_ME,
  fileIds: [],
  uploads: [],
};

function newCandidateNoteTransform(
  data: UpdateNoteFormFields
): CandidateNoteRequest {
  return {
    body: data.body,
    visibility: data.visibility.value,
    fileIds: data.fileIds,
  };
}
