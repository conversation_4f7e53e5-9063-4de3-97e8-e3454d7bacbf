import React, { useEffect, useState } from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Button from '@shared/uikit/Button';
import { useSearchDispatch } from '@shared/contexts/search/search.provider';
import removeEmptyFromObject from '@shared/utils/toolkit/removeEmptyFromObject';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { useSearchParams } from 'next/navigation';
import { searchFilterQueryParams } from '@shared/constants/search';
import Skeleton from './CandidateSavedSearchFilters.skeleton';
import classes from './index.module.scss';
import {
  getGroupFilters,
  mergeItemWithFilters,
} from '@shared/components/molecules/GlobalSearchInput/utils/savedCandidateFilters';

interface SavedSearchFilterItem {
  id: string;
  createdDate?: string;
  name: string;
  pageId?: string;
  source?: string;
  userId?: string;
  text?: string;
  [key: string]: any;
}

export interface SearchFiltersProps {
  items: Array<SavedSearchFilterItem>;
  isLoading: boolean;
  filters: any[];
}

const CandidateSavedSearchFilters: React.FC<SearchFiltersProps> = ({
  items,
  isLoading,
  filters,
}) => {
  const { t } = useTranslation();
  const searchDispatch = useSearchDispatch();
  const { handleChangeParams } = useCustomParams();
  const searchParams = useSearchParams();
  const [redirecting, setRedirecting] = useState(false);

  const onClickHandler = (item: SavedSearchFilterItem) => () => {
    const mergedItems = mergeItemWithFilters(item, filters);

    searchDispatch({
      type: 'SET_SAVE_SEARCH_FILTER_MODAL_DATA',
      payload: { isOpen: true, item: mergedItems },
    });
  };
  const saveFilterId = searchParams.get('saveFilterId');
  const newSaved = searchParams.get('newSaved');
  const selectFirst = searchParams.get('selectFirst');
  const isSelected = (id: string) => id === saveFilterId;

  const onSetFilterHandler =
    ({
      id,
      createdDate,
      name,
      pageId,
      source,
      userId,
      text,
      ...rest
    }: SavedSearchFilterItem) =>
    () => {
      const values = Object.keys(rest)?.reduce(
        (acc, key) => {
          const value = rest?.[key];
          const filterValue = Array.isArray(value)
            ? value?.filter(Boolean).join(',')
            : value;

          return !hasValue(filterValue) ? acc : { ...acc, [key]: filterValue };
        },
        { [searchFilterQueryParams.query]: text || '', saveFilterId: id }
      );

      handleChangeParams({
        add: { refresh: 'true' },
        replace: values,
        remove: ['page', 'currentEntityId'],
      });
    };

  useEffect(() => {
    if (!items?.length) return;
    if (isLoading) return;
    if (saveFilterId && !selectFirst && !newSaved) return;
    setRedirecting(true);

    setTimeout(() => {
      const item = newSaved ? items[items?.length - 1] : items?.[0];

      if (!item) return;

      const cleanedItem = removeEmptyFromObject(item);
      const { id, createdDate, name, pageId, source, userId, text, ...rest } =
        cleanedItem;

      const values = Object.keys(rest)?.reduce(
        (acc, key) => {
          const value = rest?.[key];
          const filterValue = Array.isArray(value)
            ? value?.filter(Boolean).join(',')
            : value;

          return !hasValue(filterValue) ? acc : { ...acc, [key]: filterValue };
        },
        { [searchFilterQueryParams.query]: text || '', saveFilterId: id }
      );

      handleChangeParams({
        add: { refresh: 'true' },
        replace: values,
        remove: ['page', 'currentEntityId'],
      });
    }, 300);

    setTimeout(() => {
      setRedirecting(false);
    }, 500);
  }, [items, newSaved, isLoading, selectFirst]);

  return (
    <Flex
      className={cnj(
        classes.searchFilterContent,
        classes.savedSearchHeader,
        'gap-8'
      )}
    >
      {isLoading || redirecting ? (
        <Skeleton />
      ) : (
        items.map((item) =>
          isSelected(item.id) ? (
            <Button
              onClick={onClickHandler(item)}
              key={item.id}
              className="!pr-[4px]"
              rightSvg={
                <IconButton
                  className="ml-12"
                  name="edit"
                  size="sm11"
                  colorSchema="graySecondary"
                  variant="circle"
                />
              }
              shape="capsule"
              label={item.name}
              schema="light-gray"
            />
          ) : (
            <Button
              onClick={onSetFilterHandler(removeEmptyFromObject(item))}
              key={item.id}
              shape="capsule"
              label={item.name}
              schema="secondary-dark"
            />
          )
        )
      )}

      <Button
        className="!hidden ml-auto border border-techGray_20 border-solid"
        leftIcon="drag"
        leftType="far"
        label={t('re_order')}
        schema="ghost"
      />
    </Flex>
  );
};
export default CandidateSavedSearchFilters;

function hasValue(value: string | any[]) {
  return Array.isArray(value) ? Object.keys(value)?.length > 0 : !!value;
}
