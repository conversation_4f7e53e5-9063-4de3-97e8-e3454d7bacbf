import React, { useMemo } from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import { QueryKeys } from '@shared/utils/constants';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import { getAllSavedFilters } from '@shared/utils/api/candidates';
import classes from './index.module.scss';
import List from './CandidateSavedSearchFilters.list';
import Empty from './CandidateSavedSearchFilters.empty';
import { processBusinessFilterGroups } from '../SearchFiltersHeader/processBusinessFilterGroups';
import { processUserFilterGroups } from '../SearchFiltersHeader/processUserFilterGroups';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import { getGroupFilters } from '@shared/components/molecules/GlobalSearchInput/utils/savedCandidateFilters';

export interface SearchFiltersProps {
  isLoading: boolean;
  groups: Record<string, any>[];
}

const CandidateSavedSearchFilters: React.FC<SearchFiltersProps> = ({
  isLoading: parentIsLoading,
  groups,
}) => {
  const { businessPage } = useGetAppObject();

  const { data: items = [], isLoading } = useReactQuery({
    action: {
      key: [QueryKeys.getAllSavedFilters, businessPage?.id],
      apiFunc: getAllSavedFilters,
    },
  });

  const filters = getGroupFilters({ groups, isLoading, isBusinessApp });

  return (
    <Flex className={cnj(classes.linksRootShrink, classes.borderBottom)}>
      {items.length || isLoading ? (
        <List
          isLoading={isLoading || parentIsLoading}
          items={items}
          filters={filters}
        />
      ) : (
        <Empty />
      )}
    </Flex>
  );
};
export default CandidateSavedSearchFilters;
