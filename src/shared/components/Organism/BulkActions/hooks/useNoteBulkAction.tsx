import { TwoButtonFooter } from '@shared/components/Organism/MultiStepForm/ProfileSections/Components/TwoButtonFooter';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import Icon from '@shared/uikit/Icon';
import Tooltip from '@shared/uikit/Tooltip';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { MultiStepFormProps } from '@shared/components/Organism/BulkActions/hooks/types';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type NoteBulkActionProps = {
  headerTitle: string;
  submitLabel: string;
  cancelLabel: string;
  stepKey: string;
  backStep: number;
  descriptionMaxLength: number;
};

export function useNoteBulkAction({
  backStep,
  stepKey,
  cancelLabel,
  descriptionMaxLength,
  headerTitle,
  submitLabel,
}: NoteBulkActionProps): SingleDataItem[] {
  const { t } = useTranslation();

  const visibilityOptions = [
    {
      value: 'TEAM',
      label: t('team'),
      rightComponent: (
        <Tooltip
          placement="top"
          trigger={
            <Icon color="smoke_coal" type="fal" name="info-circle" size={15} />
          }
        >
          {t('visible_to_your_team')}
        </Tooltip>
      ),
    },
    {
      value: 'ONLY_ME',
      label: t('only_me'),
      rightComponent: (
        <Tooltip
          placement="top"
          trigger={
            <Icon color="smoke_coal" type="fal" name="info-circle" size={15} />
          }
        >
          {t('visible_to_you_only')}
        </Tooltip>
      ),
    },
  ];

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: headerTitle,
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep(0),
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <TwoButtonFooter
      submitLabel={submitLabel}
      secondaryButtonLabel={cancelLabel}
      secondaryButtonOnClick={() => setStep(backStep)}
    />
  );

  const data: Array<SingleDataItem> = [
    {
      stepKey,
      getHeaderProps,

      renderBody: () => (
        <DynamicFormBuilder
          className="bg-gray_5 p-20 rounded-xl "
          groups={[
            {
              formGroup: {
                color: 'smoke_coal',
                title: t('visibility'),
              },
              classNames: {
                root: '!flex !flex-row gap-10',
                itemWrapper: '!mb-0',
              },
              label: t('visibility'),
              cp: 'radioGroup',
              name: 'visibility',
              options: visibilityOptions,
              getValue: () => 'ALL',
              schema: 'semi-transparent',
              isDefaultValue: 'ALL',
            },
            {
              name: 'body',
              cp: 'richtext',
              label: t(`Write note`),
              showEmoji: false,
              visibleOptionalLabel: false,
              maxLength: descriptionMaxLength,
              className: 'bg-popOverBg_white !h-[126px] overflow-auto',
            },
            {
              name: 'fileIds',
              cp: 'attachmentPicker',
              label: t('attachment'),
              visibleOptionalLabel: false,
              wrapStyle: 'mt-12',
            },
          ].filter(Boolean)}
        />
      ),

      renderFooter,
    },
  ];

  return data;
}
