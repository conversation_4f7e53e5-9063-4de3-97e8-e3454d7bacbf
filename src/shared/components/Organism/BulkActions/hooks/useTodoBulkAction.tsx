import { useEffect, useState } from 'react';
import useGetPageMembers from '@shared/hooks/api-hook/useGetPageMembers';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import fuseSearch from '@shared/uikit/utils/fuseSearch';
import { schedulesDb } from '@shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { TwoButtonFooter } from '../../MultiStepForm/ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '@shared/components/Organism/BulkActions/hooks/types';
import type { PageMemberType } from '@shared/hooks/api-hook/useGetPageMembers';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type TodoBulkActionProps = {
  headerTitle: string;
  submitLabel: string;
  cancelLabel: string;
  stepKey: string;
  backStep: number;
  descriptionMaxLength: number;
};

export function useTodoBulkAction({
  backStep,
  cancelLabel,
  headerTitle,
  submitLabel,
  stepKey,
  descriptionMaxLength,
}: TodoBulkActionProps): SingleDataItem[] {
  const { t } = useTranslation();

  const [filteredOption, setFilteredOption] = useState<PageMemberType[]>([]);
  const { data: members = [] } = useGetPageMembers({
    enabled: true,
    onSuccess: (data) => setFilteredOption(data),
  });
  const search = fuseSearch(members, {
    keys: ['user.fullName', 'user.username'],
  });

  const onChangeInput = (input: string) => {
    const items = search(input).slice(0, 10);
    setFilteredOption(items);
  };

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: headerTitle,
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep(0),
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <TwoButtonFooter
      submitLabel={submitLabel}
      secondaryButtonLabel={cancelLabel}
      secondaryButtonOnClick={() => setStep(backStep)}
    />
  );

  const data: Array<SingleDataItem> = [
    {
      stepKey,
      getHeaderProps,
      renderBody: () => (
        <DynamicFormBuilder
          className="gap-12 bg-gray_5 p-20 rounded-xl "
          groups={[
            {
              name: 'assigneeUserId',
              label: t('assignee'),
              cp: 'avatarAsyncAutoComplete',
              options: filteredOption?.map((item) => ({
                value: item.user?.id,
                label: item.user?.fullName,
                image: item.user?.image,
                helperText: item.user.username,
              })),
              visibleRightIcon: true,
              visibleOptionalLabel: false,
              forceVisibleError: true,
              onChangeInput,
            },
            {
              name: 'title',
              cp: 'input',
              maxLength: 30,
              label: t('title'),
              required: true,
              className: 'bg-popOverBg_white',
            },
            {
              name: 'description',
              cp: 'richtext',
              label: t(`description`),
              showEmoji: false,
              className: 'bg-popOverBg_white !h-[126px] overflow-auto',
              visibleOptionalLabel: false,
              maxLength: descriptionMaxLength,
            },
            {
              name: 'startDate',
              cp: 'datePicker',
              closeOnSelect: true,
              required: false,
              rightIconClassName: '!bg-transparent',
              variant: 'input',
              label: t('start_date'),
              className: 'bg-popOverBg_white',
            },
            {
              name: 'startTime',
              cp: 'dropdownSelect',
              rightIconClassName: '!bg-transparent',
              options: schedulesDb.timeOptions,
              label: t('start_time'),
              doNotUseTranslation: true,
              required: false,
              rightIconProps: { name: 'clock' },
              className: 'bg-popOverBg_white',
            },
            {
              name: 'endDate',
              cp: 'datePicker',
              closeOnSelect: true,
              required: false,
              rightIconClassName: '!bg-transparent',
              variant: 'input',
              label: t('end_date'),
              className: 'bg-popOverBg_white',
            },
            {
              name: 'endTime',
              cp: 'dropdownSelect',
              rightIconClassName: '!bg-transparent',
              options: schedulesDb.timeOptionsEnd,
              label: t('end_time'),
              doNotUseTranslation: true,
              required: false,
              rightIconProps: { name: 'clock' },
              className: 'bg-popOverBg_white',
            },
            {
              name: 'fileIds',
              cp: 'attachmentPicker',
              label: t('attachment'),
              visibleOptionalLabel: false,
            },
          ].filter(Boolean)}
        />
      ),

      renderFooter,
    },
  ];

  return data;
}
