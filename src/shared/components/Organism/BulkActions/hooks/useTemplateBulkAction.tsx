import Link from 'next/link';
import { TwoButtonFooter } from '@shared/components/Organism/MultiStepForm/ProfileSections/Components/TwoButtonFooter';
import { closeMultiStepForm } from '@shared/hooks/useMultiStepForm';
import Flex from '@shared/uikit/Flex';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import MenuItem from '@shared/uikit/MenuItem';
import Typography from '@shared/uikit/Typography';
import { getAllTemplates } from '@shared/utils/api/template';
import { routeNames } from '@shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { MultiStepFormProps } from '@shared/components/Organism/BulkActions/hooks/types';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type TemplateBulkAction = {
  onChange: (item: any) => void;
  additionalFields: any[];
  item: any;
  headerLabel: string;
  submitLabel: string;
  cancelLabel: string;
  stepKey: string;
  backStep: number;
  introElement?: ReactNode;
  disabledSubmit?: boolean;
  apiFunc?: any;
  normalizer?: (val: any) => void;
};

export function useTemplateBulkAction({
  onChange,
  additionalFields,
  item,
  cancelLabel,
  headerLabel,
  stepKey,
  submitLabel,
  backStep,
  introElement,
  disabledSubmit,
  apiFunc,
  normalizer,
}: TemplateBulkAction): SingleDataItem[] {
  const { t } = useTranslation();

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: headerLabel,
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep(0),
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <TwoButtonFooter
      submitLabel={submitLabel}
      disabledSubmit={disabledSubmit}
      secondaryButtonLabel={cancelLabel}
      secondaryButtonOnClick={() => setStep(backStep)}
    />
  );

  const data: Array<SingleDataItem> = [
    {
      stepKey,
      getHeaderProps,
      renderBody: ({ setFieldValue }) => (
        <Flex className="gap-20">
          {introElement}
          <MenuItem
            title={t('candidate_bulk_email_message_info')}
            iconType="fal"
            iconSize={20}
            iconName="info-circle"
            className="!bg-gray_5"
            rightElement={
              <Link
                href={routeNames.settingsTextTemplates.email}
                onClick={() => closeMultiStepForm('candidateBulkAction')}
              >
                <Typography color="brand" fontSize={15} fontWeight={700}>
                  {t('modify_template')}
                </Typography>
              </Link>
            }
          />

          <DynamicFormBuilder
            className="gap-12 bg-gray_5 p-20 rounded-xl "
            groups={[
              {
                label: t('template'),
                required: true,
                name: 'template',
                cp: 'asyncAutoComplete',
                maxLength: 100,
                apiFunc: apiFunc || getAllTemplates,
                initSearchValue: ' ',
                normalizer: (data: any) =>
                  normalizer?.(data) ||
                  data?.content?.map((item: any) => ({
                    value: item.id,
                    label: item.title,
                    ...item,
                  })),
                onChange: (item: any) => {
                  onChange(item);
                  setFieldValue?.('template', item?.id);
                },
                value: item,
                visibleOptionalLabel: false,
              },
              ...additionalFields,
            ].filter(Boolean)}
          />
        </Flex>
      ),
      renderFooter,
    },
  ];

  return data;
}
