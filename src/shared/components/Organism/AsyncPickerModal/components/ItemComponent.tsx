import { memo } from 'react';
import AvatarCard from '@shared/uikit/AvatarCard';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import type { IconName } from '@shared/uikit/Icon/types';
import type { PropsWithChildren } from 'react';

interface Props {
  title: string;
  subTitle: string;
  image?: string;
  subTitleIcon?: IconName;
  onClick?: VoidFunction;
  isCompany?: boolean;
}

function ItemComponent(props: PropsWithChildren<Props>) {
  const {
    title,
    subTitle,
    image,
    subTitleIcon,
    onClick,
    children,
    isCompany = true,
  } = props;

  return (
    <AvatarCard
      avatarProps={{
        imgSrc: image,
        bordered: false,
        isCompany,
        name: title,
      }}
      onClick={onClick}
      data={{
        title,
        subTitle: (
          <Flex className="!flex-row items-center gap-4 mt-4">
            {subTitleIcon && (
              <Icon
                name={subTitleIcon}
                type="far"
                size={16}
                color="secondaryDisabledText"
              />
            )}
            <Typography color="secondaryDisabledText" size={14} height={16}>
              {subTitle}
            </Typography>
          </Flex>
        ),
      }}
      action={<Flex className="ml-auto">{children}</Flex>}
    />
  );
}

export default memo(ItemComponent);
