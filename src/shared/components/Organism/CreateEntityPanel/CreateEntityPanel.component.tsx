import React, { useState, type MouseEvent as RMouseEvent } from 'react';
import dropRight from 'lodash/dropRight';
import last from 'lodash/last';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import FixedRightSideModal from 'shared/uikit/Modal/FixedRightSideModalDialog';
import { TOP_CREATE_BUTTON_CLASSNAME } from 'shared/constants/enums';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import ComingSoon from 'shared/svg/ComingSoon';
import useBackToModal from '@shared/hooks/useBackToModal';
import CreateEntityPanelList from './CreateEntityPanel.List';

const CreateEntityPanelSteps = {
  LIST: 'LIST',
  COMING: 'COMING',
} as const;

type IStep = keyof typeof CreateEntityPanelSteps;

type Step = {
  title?: string;
  cp?: ReactNode;
  footer?: ReactNode;
  customRender?: ReactNode;
};

const CreateEntityPanel = () => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const { setHasBackModal } = useBackToModal('createEntityPanel');

  const [cashedData, setCashedData] = useState<IStep[]>([
    CreateEntityPanelSteps.LIST,
  ]);

  const isFirsStep = cashedData.length === 1;

  const onClose = (e?: RMouseEvent<HTMLElement, MouseEvent> | null) => {
    const shouldIgnore = [
      ...Array.from(
        document.getElementsByClassName(TOP_CREATE_BUTTON_CLASSNAME)
      ),
    ]?.some((node) => node?.contains((e?.target || null) as Element));
    if (shouldIgnore) return;
    appDispatch({ type: 'TOGGLE_CREATE_ENTITY_PANEL' });
  };
  const openCustomModal = () => {
    setHasBackModal(true);
    setTimeout(() => {
      appDispatch({ type: 'TOGGLE_CREATE_ENTITY_PANEL' });
    }, 250);
  };

  const handlePrev = (e?: RMouseEvent<HTMLElement, MouseEvent> | null) => {
    if (isFirsStep) {
      onClose(e);
    }
    setCashedData(dropRight(cashedData));
  };
  const handleNext = (step: string) => {
    setCashedData((prev) => [...prev, step as IStep]);
  };

  const steps: Record<string, Step> = {
    [CreateEntityPanelSteps.LIST]: {
      cp: (
        <CreateEntityPanelList
          Steps={CreateEntityPanelSteps}
          onClick={handleNext}
          openCustomModal={openCustomModal}
        />
      ),
      title: t('create'),
    },

    [CreateEntityPanelSteps.COMING]: {
      customRender: (
        <EmptySectionInModules
          title={t('coming_up')}
          text={t('w_r_w_o_it')}
          image={<ComingSoon />}
          isFullParent
        />
      ),
      title: t('coming_up'),
    },
  };
  const { title, cp, footer, customRender } =
    steps[last(cashedData) as keyof typeof steps] || {};

  if (!cp && !customRender) return null;

  return (
    <FixedRightSideModal
      onBack={handlePrev}
      onClose={handlePrev}
      onClickOutside={handlePrev as any}
      isOpenAnimation
      wide
    >
      <ModalHeaderSimple
        title={title}
        hideBack={isFirsStep}
        noCloseButton={!isFirsStep}
      />
      {customRender || (
        <>
          <ModalBody>{cp}</ModalBody>
          {footer}
        </>
      )}
    </FixedRightSideModal>
  );
};

export default CreateEntityPanel;
