import isEmptyObjectValues from '@shared/utils/toolkit/isEmptyObjectValues';
import { hasValue } from './hasValue';

export const processUserFilterGroups = (groups: Array<Record<string, any>>) =>
  groups
    .reduce((prev: Array<any>, curr, currentIndex) => {
      const isEmpty = isEmptyObjectValues({
        val: curr.getValue(),
      });
      if (
        !curr?.alwaysShowInHeader &&
        (curr.hiddenInHeader || (isEmpty && !curr.order))
      ) {
        return prev;
      }
      const order = (curr.order || currentIndex + 7) * (isEmpty ? 1 : 10);

      return [...prev, { ...curr, isEmpty, order }];
    }, [])
    .sort((a, b) => {
      if (
        hasValue(a.getValue(), a?.isDefaultValue) &&
        !hasValue(b.getValue(), b?.isDefaultValue)
      ) {
        return -1;
      }
      if (
        hasValue(b.getValue(), b?.isDefaultValue) &&
        !hasValue(a.getValue(), a?.isDefaultValue)
      ) {
        return +1;
      }

      return 0;
    });
