import { useSearchParams } from 'next/navigation';
import React, { Fragment, useEffect, useRef } from 'react';
import { searchFilterQueryParams } from 'shared/constants/search';
import useNavigateSearchPage from 'shared/hooks/useNavigateSearchPage';
import {
  useJobsDispatch,
  useJobsState,
} from 'shared/providers/JobsPorvider/jobs.provider';
import Button from 'shared/uikit/Button';
import BaseButton from 'shared/uikit/Button/BaseButton';
import IconButton from 'shared/uikit/Button/IconButton';
import CheckBoxGroup from 'shared/uikit/CheckBoxGroup';
import Flex from 'shared/uikit/Flex';
import ListItem from 'shared/uikit/ListItem';
import FixedRightSideModal from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import PopperMenu from 'shared/uikit/PopperMenu';
import RadioGroup from 'shared/uikit/RadioGroup';
import SalaryPicker from 'shared/uikit/SalaryPicker';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import useMedia from 'shared/uikit/utils/useMedia';
import { jobsDb } from 'shared/utils/constants/enums';
import useStateCallback from 'shared/utils/hooks/useStateCallback';
import useTranslation from 'shared/utils/hooks/useTranslation';
import collectionToObjectByKey from 'shared/utils/toolkit/collectionToObjectByKey';
import isEmptyObjectValues from 'shared/utils/toolkit/isEmptyObjectValues';
import classes from './SearchFiltersHeader.item.module.scss';
import type {
  SearchFiltersQueryParamsType,
  SearchFiltersValueType,
} from 'shared/types/search';

export interface SearchFiltersHeaderItemProps {
  className?: string;
  type?: 'list' | 'radioGroup' | 'checkBoxGroup' | 'salaryPicker' | 'button';
  options?: Array<{ value: any; label: string; leftIcon?: string }>;
  name: SearchFiltersQueryParamsType;
  label?: string;
  defaultValue: SearchFiltersValueType;
  onChange?(value: { query: string; pathname: string }): void;
  value: any;
  [x: string]: any;
}

const searchEntities = collectionToObjectByKey(
  jobsDb.jobSearchOptions.searchEntities,
  'value'
);

const SearchFiltersHeaderItem: React.FC<SearchFiltersHeaderItemProps> = (
  props
) => {
  const {
    className,
    type,
    options = [],
    name,
    defaultValue,
    label,
    value,
    isDefaultValue,
    onChange,
    addToDynamicFiltersAndSetFilter,
    searchFilters,
    getLabelOrValue,
    ...rest
  } = props;
  const { t } = useTranslation();
  const jobsDispatch = useJobsDispatch();
  const popperRef = useRef();
  const navigateSearchPage = useNavigateSearchPage();
  const searchParams = useSearchParams();
  const { isMoreThanTablet } = useMedia();
  const filtersWrapperRef = useJobsState('filtersWrapperRef');
  const asyncAutoCompleteProps = rest?.asyncAutoCompleteProps;
  const placeholder = rest?.placeholder;

  const [localValues, setLocalValues] = useStateCallback(value);

  const stringValue = Array.isArray(value)
    ? value?.map?.((item) => item?.value)?.join('')
    : value;

  useEffect(() => {
    setLocalValues(value);
  }, [stringValue]);

  const commonProps = {
    options,
    labelProps: {
      font: '400',
      size: 15,
    },
  };
  const isBadgeVisible =
    !isEmptyObjectValues(value) &&
    value?.length !== 1 &&
    type === 'checkBoxGroup';

  const isLabelReplacedWithSingleChosenOption =
    type === 'checkBoxGroup' && value?.length === 1;

  const isPrimary =
    !isEmptyObjectValues(value) && type !== 'list' && !isDefaultValue;

  const isDirty = () => {
    if (type === 'checkBoxGroup') {
      return (
        value?.map((item: any) => item?.value)?.join('') !==
        localValues?.map((item: any) => item?.value)?.join('')
      );
    }

    return value !== localValues;
  };

  const isActionButtonEnabled = () => isDirty() || !!localValues?.length;

  const isSecondaryActionButtonEnabled = () => {
    if (type === 'checkBoxGroup') {
      if (!(value?.length + localValues?.length)) return true;
    } else {
      const isValueString = typeof value === 'string';
      const isLocalValueString = typeof localValues === 'string';
      if (!isValueString || !isLocalValueString) return true;
    }

    return isDirty();
  };

  const handleOptionClick = (val: Array<any> | string) => {
    setLocalValues(val);
  };

  const closePopper = () => {
    popperRef.current?.close();
  };

  const scrollToStart = () => {
    filtersWrapperRef?.current?.scrollTo?.(0, 0);
  };

  const setFilter = (val: any) => {
    addToDynamicFiltersAndSetFilter({ ...searchFilters, [name]: val } as any);
    closePopper();
    scrollToStart();
  };

  const onChangeEntity = (val: string) => {
    closePopper();
    const changeCallback = onChange ?? navigateSearchPage;

    changeCallback({
      query: searchParams.get(searchFilterQueryParams.query) || '',
      pathname: val,
    });
  };

  const showResultsHandler = () => {
    if (!isDirty()) return closePopper();
    if (type === 'checkBoxGroup') {
      const prop = getLabelOrValue(name);
      const isSame =
        localValues?.map((item: any) => item?.value)?.join?.('') ===
        value?.map((item: any) => item?.value)?.join?.('');
      if (isSame) return;
      setFilter(
        localValues?.length ? localValues?.map((i) => i[prop]) : undefined
      );
    } else {
      setFilter(localValues);
    }
  };

  const resetHandler = () => {
    if (type === 'checkBoxGroup') {
      setLocalValues([], () => {
        setFilter([]);
      });
    } else {
      setLocalValues(undefined, () => {
        setFilter(undefined);
      });
      closePopper();
      scrollToStart();
    }
  };

  const cancelHandler = () => {
    setLocalValues(value);
    closePopper();
  };

  const handleSecondaryButtonClick = isSecondaryActionButtonEnabled()
    ? cancelHandler
    : resetHandler;

  const setPopperStatus = (payload: boolean) => () => {
    jobsDispatch({ type: 'SET_IS_POPPER_OPEN', payload });
  };

  const onClickApplyAt = () => {
    setFilter(!value);
  };

  const onClickOutside = () => {
    if (!isMoreThanTablet) return;
    showResultsHandler();
  };

  const handleBackModal = () => {
    const isSame =
      localValues?.map((item: any) => item?.value)?.join?.('') ===
      value?.map((item: any) => item?.value)?.join?.('');
    if (!isSame) setLocalValues(value);
    closePopper();
  };

  if (type === 'button') {
    return (
      <BaseButton
        onClick={onClickApplyAt}
        className={cnj(
          classes.buttonRoot,
          classes.applyAt,
          value && classes.activeButtonRoot,
          className
        )}
      >
        <Typography
          font="700"
          size={15}
          height={18}
          className={cnj(classes.label, value && classes.activeLabel)}
        >
          {label}
        </Typography>
      </BaseButton>
    );
  }

  const isModalView =
    !isMoreThanTablet && type === 'checkBoxGroup' && !!asyncAutoCompleteProps;

  const Tag = !isModalView ? Fragment : FixedRightSideModal;
  const Footer = !isModalView ? Flex : ModalFooter;

  const secondaryButtonLabel = isSecondaryActionButtonEnabled()
    ? t('cancel')
    : t('reset');

  return (
    <PopperMenu
      noBottomSheetForMobile={isModalView}
      bottomSheetClassName={classes.bottomSheetClassName}
      ref={popperRef}
      placement="bottom-start"
      menuClassName={classes.menuClassName}
      disableCloseOnClickInSide
      disableCloseOnClickOutSide={!isMoreThanTablet}
      onCloseOutside={() => {
        onClickOutside();
        setPopperStatus(false)();
      }}
      onClose={setPopperStatus(false)}
      onOpen={setPopperStatus(true)}
      disablePortal={rest?.disabled}
      buttonComponent={(visible: boolean) => (
        <BaseButton
          className={cnj(
            classes.buttonRoot,
            type === 'list' && classes.listButtonRoot,
            isPrimary && classes.activeButtonRoot,
            visible && classes.visibleButtonRoot,
            rest?.disabled && classes.disableButtonRoot,
            className
          )}
          disabled={rest?.disabled}
        >
          {name === searchFilterQueryParams.searchEntity && (
            <IconButton
              noHover
              type="far"
              size="sm"
              name={searchEntities[value]?.leftIcon}
              className={classes.leftIcon}
              colorSchema="semi-transparent"
              iconProps={{
                color: 'white',
              }}
            />
          )}
          {isBadgeVisible && (
            <Flex className={classes.badge}>
              <Typography font="700" size={14} color="brand">
                {value?.length}
              </Typography>
            </Flex>
          )}
          <Typography
            font="700"
            size={15}
            className={cnj(
              classes.label,
              type === 'list' && classes.listLabel,
              isPrimary && classes.activeLabel
            )}
          >
            {isLabelReplacedWithSingleChosenOption
              ? t(value?.[0]?.label)
              : t(label)}
          </Typography>
          <IconButton
            type="fas"
            size="sm"
            noHover
            name={visible ? 'chevron-up' : 'chevron-down'}
            className={cnj(classes.chevronIcon)}
            iconProps={{
              color: type === 'list' || isPrimary ? 'white' : 'brand',
            }}
          />
        </BaseButton>
      )}
    >
      <Tag>
        {isModalView ? (
          <ModalHeaderSimple
            visibleHeaderDivider
            title={t(label || '')}
            hideBack={false}
            backButtonProps={{
              onClick: handleBackModal,
            }}
            noCloseButton
          />
        ) : !isMoreThanTablet ? (
          <Flex className={classes.bottomSheetTitleWrapper}>
            <Typography font="700" size={16} height={18} color="smoke_coal">
              {t(label || '')}
            </Typography>
          </Flex>
        ) : (
          <></>
        )}
        <Flex className={classes.childrenWrapper}>
          {type === 'checkBoxGroup' && (
            <CheckBoxGroup
              onChange={handleOptionClick}
              value={localValues}
              className={classes.checkBoxGroup}
              {...commonProps}
              {...(asyncAutoCompleteProps && {
                asyncAutoCompleteProps: {
                  maxLength: 100,
                  styles: { options: classes.stickyAutoCompleteOptions },
                  ...asyncAutoCompleteProps,
                },
                classNames: {
                  container: classes.checkboxGroupContainer,
                  inputContainer: classes.inputContainer,
                  itemWrapper: classes.itemWrapper,
                },
                alwaysShowAutoCompleteInfo: true,
                placeholder,
              })}
              withConfirmation={false}
              labelProps={{
                color: 'disabledGray_graphene',
              }}
            />
          )}
          {type === 'radioGroup' && (
            <RadioGroup
              value={localValues}
              keepOriginalLabel={isDefaultValue}
              onChange={handleOptionClick}
              {...commonProps}
              className={classes.checkBoxGroup}
              classNames={{
                itemWrapper: classes.itemWrapper,
              }}
              labelProps={{
                color: 'disabledGray_graphene',
              }}
              label={label}
            />
          )}
          {type === 'list' && (
            <Flex className={classes.listWrapper}>
              {options.map(({ value: v, leftIcon, label: lbl }) => {
                const isActive = v === value;

                return (
                  <ListItem
                    key={v}
                    hover
                    leftSvg={
                      leftIcon ? (
                        <IconButton
                          colorSchema={isActive ? 'white' : 'smoke_coal'}
                          className={cnj(
                            classes.listItemIconButton,
                            isActive && classes.listItemIconButton_active
                          )}
                          name={leftIcon}
                          size="md20"
                          type="far"
                          variant="rectangle"
                        />
                      ) : null
                    }
                    label={t(lbl)}
                    labelSize={16}
                    className={cnj(
                      classes.listItem,
                      isActive && classes.listItem_active
                    )}
                    labelColor={isActive ? 'white' : 'smoke_coal'}
                    labelsContainerClassName={classes.labelsContainer}
                    labelFont="700"
                    onClick={() => onChangeEntity(v)}
                    hasNarrowLabel
                    narrow
                    labelClassName={classes.listItemLabel}
                  />
                );
              })}
            </Flex>
          )}
          {type === 'salaryPicker' && (
            <Flex className={classes.salaryPickerWrapper}>
              <SalaryPicker
                data={props?.data}
                value={value}
                onChange={handleOptionClick}
                label={label || ''}
              />
            </Flex>
          )}
        </Flex>
        {[
          'checkBoxGroup',
          'sliderPicker',
          'radioGroup',
          'salaryPicker',
        ].includes(type) && (
          <Footer
            className={cnj(
              isModalView ? classes.footer : classes.buttonsWrapper
            )}
          >
            <Button
              onClick={handleSecondaryButtonClick}
              fullWidth
              schema="ghost"
              labelProps={{ color: 'primaryText' }}
              label={secondaryButtonLabel}
            />
            <Flex className={classes.divider} />
            <Button
              disabled={!isActionButtonEnabled()}
              onClick={showResultsHandler}
              fullWidth
              label={t('show_results')}
            />
          </Footer>
        )}
      </Tag>
    </PopperMenu>
  );
};

export default SearchFiltersHeaderItem;
