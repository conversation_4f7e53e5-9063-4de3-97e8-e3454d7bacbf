import React, { useRef } from 'react';
import PopperMenu from 'shared/uikit/PopperMenu';
import Flex from 'shared/uikit/Flex';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Button from '@shared/uikit/Button';
import PopperItem from '@shared/uikit/PopperItem';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { searchFilterQueryParams } from '@shared/constants/search';
import classes from './CandidateSearchTypePicker.module.scss';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';

const options = [
  {
    value: 'every_one',
    label: 'every_one',
    secondaryLabel: 'all_candidates_within_t_s',
  },
  {
    value: 'interacted',
    label: 'Interacted',
    secondaryLabel: 'candidates_y_h_inter_w',
  },
  {
    value: 'saved',
    label: 'saved',
    secondaryLabel: 'y_s_searches',
  },
  {
    value: 'saved_candidate',
    label: 'saved_candidate',
    secondaryLabel: 'saved_candidate_secondary',
  },
];

interface CandidateSearchTypePickerProps {
  value: any;
}

const CandidateSearchTypePicker: React.FC<
  CandidateSearchTypePickerProps
> = () => {
  const getQueryValue = useGetNormalizedArrayQuery();

  const value =
    getQueryValue(searchFilterQueryParams.candidateSearchType) || 'every_one';
  const popperRef = useRef<any>(null);
  const { t } = useTranslation();
  const { handleChangeParams } = useCustomParams();

  const onClickOutside = () => {
    popperRef.current?.close?.();
  };

  const onChange = (val) => {
    handleChangeParams({
      add: {
        refresh: 'true',
        [searchFilterQueryParams.candidateSearchType]: val,
      },
    });
    popperRef.current?.close?.();
  };

  return (
    <PopperMenu
      bottomSheetClassName={classes.bottomSheetClassName}
      ref={popperRef}
      placement="bottom-start"
      menuClassName={classes.menuClassName}
      disableCloseOnClickInSide
      onCloseOutside={onClickOutside}
      buttonComponent={(visible: boolean) => (
        <Button
          className="gap-8 !px-8 !h-[40px] !rounded-[0px] !w-[130px]"
          labelProps={{
            isTruncated: true,
          }}
          label={`${t(value)}...`}
          rightIcon={visible ? 'chevron-up' : 'chevron-down'}
          schema="semi-transparent3"
        />
      )}
    >
      <Flex className={classes.listWrapper}>
        {options.map(({ value: v, secondaryLabel, label }) => {
          const isActive = v === value;
          return (
            <PopperItem
              key={v}
              label={t(label)}
              secondaryLabel={t(secondaryLabel)}
              onClick={() => onChange(v)}
              className={isActive ? '!bg-brand_10' : 'bg-gray_5'}
              labelProps={{
                font: 'bold',
                color: isActive ? 'brand' : 'smoke_coal',
              }}
              secondaryLabelProps={{ color: 'secondaryDisabledText' }}
            />
          );
        })}
      </Flex>
    </PopperMenu>
  );
};

export default CandidateSearchTypePicker;
