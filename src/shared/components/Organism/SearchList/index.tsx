import {
  useRef,
  useEffect,
  useMemo,
  useState,
  type ReactElement,
  type MouseEvent,
} from 'react';
import { PROFILE_SCROLL_WRAPPER } from '@shared/constants/enums';
import { useAuthState } from '@shared/contexts/Auth/auth.provider';
import useSearchFilters from '@shared/hooks/searchFilters/useSearchFilters';
import { getPortal } from '@shared/utils/getAppEnv';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import usePluralize from '@shared/utils/toolkit/usePluralize';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Flex from 'shared/uikit/Flex';
import Pagination from 'shared/uikit/Pagination';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { ResetFiltersCard } from '../SearchCard';
import classes from './index.module.scss';
import SearchListHeader from './partials/SearchListHeader';
import SearchListItemSkeleton from './SearchListItem.skeleton';
import type {
  ISearchEntity,
  SearchListEntityData,
} from '@shared/hooks/searchFilters/useSearchResultWithFilters';

interface SearchListProps<DataType> {
  title?: ReactNode;
  locationTitle?: string;
  headerElement?: ReactNode;
  footer?: ReactNode;
  data?: Array<DataType>;
  isLoading?: boolean;
  totalElements?: number;
  renderItem: (item: DataType, index: number, props: any) => ReactElement;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  noTopBottomPadding?: boolean;
  entity?: ISearchEntity;
  visiblePagination?: boolean;
  className?: {
    root?: string;
    header?: string;
    pagination?: string;
  };
  emptyList?: ReactNode;
  noItemButtonAction?: boolean;
  parentPage?: number;
  scrollToTopWhenClick?: boolean;
  innerList?: boolean;
  ItemSkeleton?: (props: any) => JSX.Element;
  showCustomComponent?: boolean;
  showCurrentEntityFirst?: boolean;
  hasSubTitle?: boolean;
}
const skeletons = Array.from({ length: 10 }, (_, i) => i);

export default function SearchList<D extends SearchListEntityData<any> = any>({
  title,
  locationTitle,
  headerElement,
  data,
  isLoading,
  totalElements,
  hasSubTitle = true,
  renderItem,
  totalPages,
  onPageChange,
  footer,
  pathnameToNavigate,
  noTopBottomPadding,
  entity,
  visiblePagination = true,
  className,
  emptyList,
  noItemButtonAction = false,
  parentPage,
  scrollToTopWhenClick = false,
  innerList,
  ItemSkeleton,
  showCustomComponent = false,
  showCurrentEntityFirst,
}: SearchListProps<D>) {
  const { t } = useTranslation();
  const wrapRef = useRef<HTMLDivElement>(null);
  const { handleChangeParams, allParams } = useCustomParams();
  const { currentEntityId, page = 0 } = allParams;
  const { resetFilters } = useSearchFilters();
  const isLoggedIn = useAuthState('isLoggedIn');
  const portal = getPortal();
  const pluralize = usePluralize();

  const onclickHandler = (item: D) => (event: MouseEvent<HTMLElement>) => {
    if (scrollToTopWhenClick) {
      document.getElementById(PROFILE_SCROLL_WRAPPER)?.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
    if (noItemButtonAction) return;

    if (!event.target?.closest('.selectable-text')) {
      if (window?.getSelection) {
        window.getSelection()?.removeAllRanges();
      }
    }
    console.warn("I've remove handleCardClick, please check if you need it");
  };

  const onPageChangeHandler = (pgNumber: number) => {
    wrapRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
    if (!innerList)
      handleChangeParams({
        add: { page: `${pgNumber - 1}` },
        remove: ['currentEntityId'],
      });
    onPageChange?.(pgNumber - 1);
  };

  const [firstEntityId, setFirstEntityId] = useState(currentEntityId);
  const sortedItems = useMemo(() => {
    if (!data) return [] as D[];
    if (!firstEntityId) return data;

    return data?.sort(({ id = '' }) => (firstEntityId === id ? -1 : 0));
  }, [data, firstEntityId]);

  useEffect(() => {
    if (showCurrentEntityFirst) setFirstEntityId(currentEntityId);
  }, [currentEntityId, showCurrentEntityFirst]);

  return (
    <Flex
      className={cnj(
        classes.flex1,
        !noTopBottomPadding && classes.wrapper,
        className?.root
      )}
      ref={wrapRef}
    >
      {title ? (
        <SearchListHeader
          title={title}
          locationTitle={locationTitle}
          subTitle={hasSubTitle ? pluralize('result', totalElements) : ''}
          isLoading={isLoading}
          className={className?.header}
          showCustomComponent={showCustomComponent}
          footer={footer}
        >
          {headerElement}
        </SearchListHeader>
      ) : null}
      {isLoading
        ? skeletons?.map((item: number) =>
            ItemSkeleton ? (
              <ItemSkeleton key={`${entity}_${item}`} />
            ) : (
              <SearchListItemSkeleton
                key={`${entity}_${item}`}
                type={entity as any}
              />
            )
          )
        : sortedItems?.length
          ? sortedItems?.map((item, index) => (
              <BaseButton
                onClick={onclickHandler(item)}
                className={cnj(classes.itemClassName)}
                key={`${item?.id}_${item?.title}_${index}`}
                disabled={!isLoggedIn}
              >
                {renderItem(item, index, {
                  className: classes.item,
                  isSelected:
                    (index === 0 && !currentEntityId) ||
                    currentEntityId === item?.id,
                  isPage: item?.type === 'PAGE',
                })}
              </BaseButton>
            ))
          : emptyList || null}
      {!isLoading && visiblePagination && Number(totalPages) > 1 ? (
        <Pagination
          key={parentPage ?? page}
          defaultPage={Number(parentPage !== undefined ? parentPage : page) + 1}
          className={cnj(
            classes.paginationFooter,
            className?.pagination,
            innerList && '!mb-0'
          )}
          onPageChange={onPageChangeHandler}
          totalPagesCount={Number(totalPages)}
        />
      ) : isLoading ? null : portal === 'user' && title ? (
        <ResetFiltersCard
          className={classes.resetFiltersCard}
          text={t('looking_for_more')}
          buttonProps={{ label: t(`all_${entity}`), onClick: resetFilters }}
        />
      ) : null}
    </Flex>
  );
}
