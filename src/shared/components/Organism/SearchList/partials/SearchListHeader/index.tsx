import type { PropsWithChildren } from 'react';
import React from 'react';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Skeleton from 'shared/uikit/Skeleton';
import classes from './index.module.scss';

type Props = {
  title: ReactNode;
  subTitle: string;
  locationTitle?: string;
  isLoading?: boolean;
  className?: string;
  showCustomComponent?: boolean;
  footer?: ReactNode;
};

const SearchListHeader = ({
  title,
  subTitle,
  locationTitle,
  isLoading,
  className,
  children,
  footer,
  showCustomComponent,
}: PropsWithChildren<Props>): JSX.Element => {
  const { t } = useTranslation();

  if (showCustomComponent) {
    return (
      <InfoCard
        {...{
          wrapperClassName: classes.wrapperClassName,
          disabledHover: true,
          className,
        }}
      >
        {children}
      </InfoCard>
    );
  }

  return (
    <InfoCard
      {...{
        className,
        wrapperClassName: classes.wrapperClassName,
        disabledHover: true,
        subTitle: t('no_desc_ent'),
        value: (
          <Flex flexDir="row" className={classes.wrapper}>
            <Flex>
              <Flex className={classes.textWrapper}>
                {isLoading ? (
                  <Skeleton className={classes.skeletonTitle} />
                ) : (
                  <Typography
                    size={16}
                    font="700"
                    height={19}
                    color="smoke_coal"
                    className={classes.title}
                  >
                    {title}
                    {locationTitle ? (
                      <>
                        <span>
                          &nbsp;
                          <Typography
                            size={16}
                            font="400"
                            height={19}
                            color="smoke_coal"
                            className={classes.inline}
                          >
                            {t('in')}
                          </Typography>
                        </span>
                        <span>
                          &nbsp;
                          <Typography
                            size={16}
                            font="700"
                            height={19}
                            color="smoke_coal"
                            className={classes.inline}
                          >
                            {locationTitle}
                          </Typography>
                        </span>
                      </>
                    ) : null}
                  </Typography>
                )}
              </Flex>
              {isLoading ? (
                <Skeleton className={classes.skeletonSubTitle} />
              ) : (
                <Typography
                  size={14}
                  font="400"
                  height={16}
                  color="secondaryDisabledText"
                >
                  {subTitle}
                </Typography>
              )}
            </Flex>
          </Flex>
        ),
      }}
    >
      {children}
      {footer}
    </InfoCard>
  );
};

export default SearchListHeader;
