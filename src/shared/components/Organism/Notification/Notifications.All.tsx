import { type FC } from 'react';
import NotificationVariants from 'shared/utils/constants/NotificationVariants';
import { type NotificationType } from '@shared/types/notification';
import PostGotReaction from './components/PostGotReaction';
import PostGotComment from './components/PostGotComment';
import CommentGotReaction from './components/CommentGotReaction';
import CommentGotReply from './components/CommentGotReply';
import PostGetShared from './components/PostGetShared';
import TaggedOnPost from './components/TaggedOnPost';
import MentionedOnPost from './components/MentionedOnPost';
import AddProfilePhoto from './components/AddProfilePhoto';
import AddHeaderImage from './components/AddHeaderImage';
import AddProfileInformation from './components/AddProfileInformation';
import NewBirthday from './components/NewBirthday';
import PersonYouMayKnow from './components/PersonYouMayKnow';
import PageYouMayKnow from './components/PageYouMayKnow';
import NewPageRoleAssigned from './components/NewPageRoleAssigned';
import NewFollower from './components/NewFollower';
import FollowRequest from './components/FollowRequest';
import PasswordChanged from './components/PasswordChanged';
import NewDeviceLoggedIn from './components/NewDeviceLoggedIn';
import PublishedNewPage from './components/PublishedNewPage';
import PageRoleAccepted from './components/PageRoleAccepted';
import PageRoleDeclined from './components/PageRoleDeclined';
import FollowRequestAccepted from './components/FollowRequestAccepted';
import PersonFromYourCompany from './components/PersonFromYourCompany';
import PersonFromYourSchool from './components/PersonFromYourSchool';
import YouMentionedOnComment from './components/YouMentionedOnComment';
import SavedButNotAppliedJobFounded from './components/SavedButNotAppliedJobFounded';
import ReminderAlarmed from './components/ReminderAlarmed';
// import JobCandidateRecommendationUpdated from './components/JobCandidateRecommendationUpdated';
import TopJobSuggestionFounded from './components/TopJobSuggestionFounded';
import JobApplicationCounterChanged from './components/JobApplicationCounterChanged';
import TaskUpdated from './components/TaskUpdated';
import TaskRemoved from './components/TaskRemoved';
import TaskGotComment from './components/TaskGotComment';
import AttendeeDeclined from './components/AttendeeDeclined';
import AttendeeAccepted from './components/AttendeeAccepted';
import MeetingRemoved from './components/MeetingRemoved';
import MeetingUpdated from './components/MeetingUpdated';
import MeetingCreated from './components/MeetingCreated';
import MeetingAlarmed from './components/MeetingAlarmed';
import JobClosed from './components/JobClosed';
import JobAlertMatched from './components/JobAlertMatched';
import InvitationCountReached from './components/InvitationCountReached';
import InvitationFailure from './components/InvitationFailure';
import InsufficientProfileSkill from './components/InsufficientProfileSkill';
import ClosestUserCommentGotReaction from './components/ClosestUserCommentGotReaction';
import YourInvolvedCommentGotReaction from './components/YourInvolvedCommentGotReaction';
import InCompleteProfileInfoFound from './components/InCompleteProfileInfoFound';
import InCompleteProfileImageFound from './components/InCompleteProfileImageFound';
import InCompletePageContactInfoFound from './components/InCompletePageContactInfoFound';
import InCompletePageContactImageFound from './components/InCompletePageContactImageFound';
import YourClosestUserMentionedInCommentInTheirPost from './components/YourClosestUserMentionedInCommentInTheirPost';
import OthersMentionedInCommentInYourPost from './components/OthersMentionedInCommentInYourPost';
import YouMentionedInCommentInOtherPost from './components/YouMentionedInCommentInOtherPost';
import YouMentionedInCommentInYourPost from './components/YouMentionedInCommentInYourPost';
import YourClosestUserMentionedInOthersPost from './components/YourClosestUserMentionedInOthersPost';
import YouMentionedInOthersPost from './components/YouMentionedInOthersPost';
import NewPortalAccessAssigned from './components/NewPortalAccessAssigned';
import OtherCommentInYouPostGotReply from './components/OtherCommentInYouPostGotReply';
import YourCommentInOthersPostGotReply from './components/YourCommentInOthersPostGotReply';
import YourCommentInYourPostGotReply from './components/YourCommentInYourPostGotReply';
import YourClosestUserCommentedOnOthersPost from './components/YourClosestUserCommentedOnOthersPost';
import YourCommentInOthersPostGotReaction from './components/YourCommentInOthersPostGotReaction';
import OthersCommentInYourPostGotReaction from './components/OthersCommentInYourPostGotReaction';
import YourCommentInYourPostGotReaction from './components/YourCommentInYourPostGotReaction';
import YourClosestUserPostGotReaction from './components/YourClosestUserPostGotReaction';
import YourInvolvedPostGotReaction from './components/YourInvolvedPostGotReaction';
import AttendeeAdded from './components/AttendeeAdded';
import UnsyncCalendarDetected from './components/UnsyncCalendarDetected';
import OthersCommentThatYouMentionedGotReply from './components/OthersCommentThatYouMentionedGotReply';
import InCompletePageHeaderFound from './components/InCompletePageHeaderFound';
import TaskCreated from './components/TaskCreated';
import TaskCommentUpdated from './components/TaskCommentUpdated';
import TaskCommentRemoved from './components/TaskCommentRemoved';
import MeetingBooked from './components/MeetingBooked';
import ManageProfileVisibility from './components/ManageProfileVisibility';
import ResumeNotParsed from './components/ResumeNotParsed';
import TaskAssigneeDeclined from './components/TaskAssigneeDeclined';
import TaskAssigneeAccepted from './components/TaskAssigneeAccepted';
import PlanActivated from './components/PlanActivated';
import PlanRenewed from './components/PlanRenewed';
import PlanDowngraded from './components/PlanDowngraded';
import PlanCanceled from './components/PlanCanceled';
import CandidateJoined from './components/CandidateJoined';
import VendorRelationshipRequested from './components/VendorRelationshipRequested';

const cmp: {
  [key in NotificationType]: FC<any> | ((args: never) => JSX.Element);
} = {
  [NotificationVariants.YOUR_POST_GOT_REACTION]: PostGotReaction,
  [NotificationVariants.YOUR_POST_GOT_COMMENT]: PostGotComment,
  [NotificationVariants.YOUR_COMMENT_GOT_REACTION]: CommentGotReaction,
  [NotificationVariants.YOUR_COMMENT_GOT_REPLY]: CommentGotReply,
  [NotificationVariants.YOU_MENTIONED_ON_COMMENT]: YouMentionedOnComment,
  [NotificationVariants.YOUR_POST_GET_SHARED]: PostGetShared,
  [NotificationVariants.YOU_TAGGED_ON_POST]: TaggedOnPost,
  [NotificationVariants.YOU_MENTIONED_ON_POST]: MentionedOnPost,
  [NotificationVariants.ADD_PROFILE_PHOTO]: AddProfilePhoto,
  [NotificationVariants.ADD_HEADER_IMAGE]: AddHeaderImage,
  [NotificationVariants.ADD_PROFILE_INFORMATION]: AddProfileInformation,
  [NotificationVariants.NEW_BIRTHDAY]: NewBirthday,
  [NotificationVariants.PEOPLE_YOU_MAY_KNOW]: PersonYouMayKnow,
  [NotificationVariants.PAGES_YOU_MAY_KNOW]: PageYouMayKnow,
  [NotificationVariants.NEW_PERSON_FROM_YOUR_SCHOOL]: PersonFromYourSchool,
  [NotificationVariants.NEW_PERSON_FROM_YOUR_COMPANY]: PersonFromYourCompany,
  [NotificationVariants.NEW_PAGE_ROLE_ASSIGNED]: NewPageRoleAssigned,
  [NotificationVariants.NEW_FOLLOWER]: NewFollower,
  [NotificationVariants.NEW_FOLLOW_REQUEST]: FollowRequest,
  [NotificationVariants.PASSWORD_CHANGED]: PasswordChanged,
  [NotificationVariants.NEW_DEVICE_LOGGED_IN]: NewDeviceLoggedIn,
  [NotificationVariants.PUBLISHED_A_NEW_PAGE]: PublishedNewPage,
  [NotificationVariants.PAGE_ROLE_ACCEPTED]: PageRoleAccepted,
  [NotificationVariants.PAGE_ROLE_DECLINED]: PageRoleDeclined,
  [NotificationVariants.YOUR_FOLLOW_REQUEST_ACCEPTED]: FollowRequestAccepted,
  [NotificationVariants.SAVED_BUT_NOT_APPLIED_JOB_FOUND]:
    SavedButNotAppliedJobFounded,
  [NotificationVariants.REMINDER_ALARMED]: ReminderAlarmed,
  // [NotificationVariants.JOB_CANDIDATE_RECOMMENDATION_UPDATED]: JobCandidateRecommendationUpdated,
  [NotificationVariants.TOP_JOB_SUGGESTION_FOUND]: TopJobSuggestionFounded,
  [NotificationVariants.JOB_APPLICANT_COUNT_CHANGED]:
    JobApplicationCounterChanged,
  [NotificationVariants.TASK_UPDATED]: TaskUpdated,
  [NotificationVariants.TASK_REMOVED]: TaskRemoved,
  [NotificationVariants.TASK_CREATED]: TaskCreated,
  [NotificationVariants.TASK_GOT_COMMENT]: TaskGotComment,
  [NotificationVariants.TASK_COMMENT_UPDATED]: TaskCommentUpdated,
  [NotificationVariants.TASK_COMMENT_REMOVED]: TaskCommentRemoved,
  [NotificationVariants.ATTENDEE_DECLINED]: AttendeeDeclined,
  [NotificationVariants.ATTENDEE_ACCEPTED]: AttendeeAccepted,
  [NotificationVariants.MEETING_REMOVED]: MeetingRemoved,
  [NotificationVariants.MEETING_UPDATED]: MeetingUpdated,
  [NotificationVariants.MEETING_CREATED]: MeetingCreated,
  [NotificationVariants.MEETING_ALARMED]: MeetingAlarmed,
  [NotificationVariants.MEETING_BOOKED]: MeetingBooked,
  [NotificationVariants.JOB_CLOSED]: JobClosed,
  [NotificationVariants.JOB_ALERT_MATCHED]: JobAlertMatched,
  [NotificationVariants.PEOPLE_INVITED]: InvitationCountReached,
  [NotificationVariants.FAILURE_INVITED]: InvitationFailure,
  [NotificationVariants.INSUFFICIENT_PROFILE_SKILL_FOUND]:
    InsufficientProfileSkill,
  [NotificationVariants.YOUR_CLOSEST_USER_COMMENT_GOT_REACTION]:
    ClosestUserCommentGotReaction,
  [NotificationVariants.YOUR_INVOLVED_COMMENT_GOT_REACTION]:
    YourInvolvedCommentGotReaction,
  [NotificationVariants.INCOMPLETE_PROFILE_INFO_FOUND]:
    InCompleteProfileInfoFound,
  [NotificationVariants.INCOMPLETE_PROFILE_IMAGE_FOUND]:
    InCompleteProfileImageFound,
  [NotificationVariants.INCOMPLETE_PAGE_CONTACT_INFO_FOUND]:
    InCompletePageContactInfoFound,
  [NotificationVariants.INCOMPLETE_PAGE_IMAGE_FOUND]:
    InCompletePageContactImageFound,
  [NotificationVariants.YOUR_CLOSEST_USER_MENTIONED_IN_COMMENT_IN_OTHERS_POST]:
    YourClosestUserMentionedInCommentInTheirPost,
  [NotificationVariants.OTHERS_MENTIONED_IN_COMMENT_IN_YOUR_POST]:
    OthersMentionedInCommentInYourPost,
  [NotificationVariants.YOU_MENTIONED_IN_COMMENT_IN_OTHERS_POST]:
    YouMentionedInCommentInOtherPost,
  [NotificationVariants.YOU_MENTIONED_IN_COMMENT_IN_YOUR_POST]:
    YouMentionedInCommentInYourPost,
  [NotificationVariants.YOU_MENTIONED_IN_OTHERS_POST]: YouMentionedInOthersPost,
  [NotificationVariants.NEW_PORTAL_ACCESS_ASSIGNED]: NewPortalAccessAssigned,
  [NotificationVariants.OTHERS_COMMENT_IN_YOU_POST_GOT_REPLY]:
    OtherCommentInYouPostGotReply,
  [NotificationVariants.YOUR_COMMENT_IN_YOUR_POST_GOT_REPLY]:
    YourCommentInYourPostGotReply,
  [NotificationVariants.YOUR_CLOSEST_USER_COMMENTED_ON_OTHERS_POST]:
    YourClosestUserCommentedOnOthersPost,
  [NotificationVariants.YOUR_COMMENT_IN_OTHERS_POST_GOT_REACTION]:
    YourCommentInOthersPostGotReaction,
  [NotificationVariants.OTHERS_COMMENT_IN_YOUR_POST_GOT_REACTION]:
    OthersCommentInYourPostGotReaction,
  [NotificationVariants.YOUR_COMMENT_IN_YOUR_POST_GOT_REACTION]:
    YourCommentInYourPostGotReaction,
  [NotificationVariants.YOUR_CLOSEST_USER_POST_GOT_REACTION]:
    YourClosestUserPostGotReaction,
  [NotificationVariants.YOUR_INVOLVED_POST_GOT_REACTION]:
    YourInvolvedPostGotReaction,
  [NotificationVariants.ATTENDEE_ADDED]: AttendeeAdded,
  [NotificationVariants.UN_SYNC_CALENDAR_DETECTED]: UnsyncCalendarDetected,
  [NotificationVariants.YOUR_CLOSEST_USER_MENTIONED_IN_OTHERS_POST]:
    YourClosestUserMentionedInOthersPost,
  [NotificationVariants.YOUR_COMMENT_IN_OTHERS_POST_GOT_REPLY]:
    YourCommentInOthersPostGotReply,
  [NotificationVariants.OTHERS_COMMENT_THAT_YOU_MENTIONED_GOT_REPLY]:
    OthersCommentThatYouMentionedGotReply,
  // Newly added notification types
  [NotificationVariants.INCOMPLETE_PAGE_HEADER_FOUND]:
    InCompletePageHeaderFound,
  [NotificationVariants.PRIVATE_USER_FOUND]: ManageProfileVisibility,
  [NotificationVariants.UNPARSED_RESUME_FOUND]: ResumeNotParsed,
  [NotificationVariants.TASK_ASSIGNEE_DECLINED]: TaskAssigneeDeclined,
  [NotificationVariants.TASK_ASSIGNEE_ACCEPTED]: TaskAssigneeAccepted,
  [NotificationVariants.PLAN_ACTIVATED]: PlanActivated,
  [NotificationVariants.PLAN_RENEWED]: PlanRenewed,
  [NotificationVariants.PLAN_DOWNGRADED]: PlanDowngraded,
  [NotificationVariants.PLAN_CANCELED]: PlanCanceled,
  [NotificationVariants.CANDIDATE_JOINED]: CandidateJoined,
  [NotificationVariants.CLIENT_RELATIONSHIP_REQUESTED]:
    VendorRelationshipRequested,
  [NotificationVariants.VENDOR_RELATIONSHIP_REQUESTED]:
    VendorRelationshipRequested,
  // [NotificationVariants.INCOMPLETE_SIGNUP_FOUND]: IncompleteSignupFound,
  // [NotificationVariants.USER_WITHOUT_ANY_PAGE_FOUND]: UserWithoutAnyPageFound,
  // [NotificationVariants.USER_SAVED_JOB_CLOSED]: UserSavedJobClosed,
  // [NotificationVariants.FOLLOWER_COUNTER_MILESTONE_REACHED]:
  //   FollowerCounterMilestoneReached,
};

export default cmp;
