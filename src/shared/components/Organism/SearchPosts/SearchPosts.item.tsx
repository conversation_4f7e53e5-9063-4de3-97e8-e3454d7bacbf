import React from 'react';
import useFeedElement from 'shared/components/Organism/FeedCard/Context/useFeedElement';
import FeedLayoutHeader from 'shared/components/Organism/FeedCard/layout/Header/header.component';
import PostThumbnailMedia from 'shared/components/Organism/PostThumbnailMedia';
import SearchCardWrapper from 'shared/components/Organism/SearchCard/SearchCard.wrapper';
import SearchPostsItemBody from 'shared/components/Organism/SearchPosts/SearchPosts.itemBody';
import postTypes from 'shared/constants/postTypes';
import Flex from 'shared/uikit/Flex';
import cnj from 'shared/uikit/utils/cnj';
import useMedia from 'shared/uikit/utils/useMedia';
import classes from './SearchPosts.item.module.scss';

interface Props {
  itemProps: any;
  classNames?: {
    wrapper?: string;
    rightSection?: string;
  };
}

const SearchPosts = ({ itemProps, classNames }: Props): JSX.Element => {
  const videoRef = React.useRef<HTMLVideoElement>();
  const { feedMediaType, isSharedPost } = useFeedElement();
  const { isTabletAndLess } = useMedia();

  const hasMedia = feedMediaType !== postTypes.TEXT;

  const setVideoRef = (ref: React.MutableRefObject<HTMLVideoElement>) => {
    videoRef.current = ref?.current;
    videoRef.current?.pause();
  };

  return (
    <SearchCardWrapper {...itemProps}>
      <Flex
        onMouseEnter={() => videoRef.current?.play()}
        onMouseLeave={() => videoRef.current?.pause()}
        className={cnj(
          classes.searchCardCardContainer,
          (hasMedia || isSharedPost) && classes.noPaddingLeft,
          classNames?.wrapper
        )}
      >
        <Flex className={classes.content}>
          {hasMedia || isSharedPost ? (
            <PostThumbnailMedia
              setVideoRef={setVideoRef}
              size="small"
              className={cnj(isTabletAndLess && classes.mobileThumbnail)}
              imgClassName={classes.imgThumbnail}
            />
          ) : null}
          <Flex className={cnj(classes.rightSection, classNames?.rightSection)}>
            <FeedLayoutHeader
              isSelected={itemProps.isSelected}
              className={classes.topSection}
              primaryTextProps={{ lineNumber: 1 }}
            />
            <Flex className={classes.bottomSection}>
              <SearchPostsItemBody />
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </SearchCardWrapper>
  );
};

export default SearchPosts;
