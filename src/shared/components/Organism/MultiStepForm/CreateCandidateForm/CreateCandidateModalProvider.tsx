import * as API from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useQueryClient } from '@tanstack/react-query';
import {
  type FC,
  type PropsWithChildren,
  createContext,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import type { CandidateFormData } from 'shared/types/candidates';
import useResponseToast from '@shared/hooks/useResponseToast';

interface CreateCandidateModalProviderProps {
  initialValue?: CandidateFormData;
}

export type DataMode = 'candidate' | 'custom' | undefined;

interface AsyncFunc<P, R> {
  (p: P): Promise<R>;
}
function wrapApiFunction<P, R>(
  fn: AsyncFunc<P, R>,
  onSuccess: (res: R) => R | void,
  onError: (error: any) => void
): AsyncFunc<P, R> {
  return (p: P) => fn(p).then(onSuccess).catch(onError);
}

interface CandidateAPIActions {
  Candidate: {
    Create: typeof API.createCandidate;
    EditBasic: typeof API.editCandidateBasic;
    EditSocial: typeof API.editCandidateSocialInfo;
    EditPreference: typeof API.editCandidatePreferenceInfo;
    EditLegal: typeof API.editCandidateLegalInfo;
    EditDemographic: typeof API.editCandidateDemographicInfo;
    EditAdditional: typeof API.editCandidateAdditionalInfo;
    SetLockMode: typeof API.setLockMode;
  };
  Experience: {
    Add: typeof API.addCandidateExperience;
    Edit: typeof API.editCandidateExperience;
    Remove: typeof API.removeCandidateExperience;
  };
  Education: {
    Add: typeof API.addCandidateEducation;
    Edit: typeof API.editCandidateEducation;
    Remove: typeof API.removeCandidateEducation;
  };
  Skill: {
    Add: typeof API.addCandidateSkill;
    Edit: typeof API.editCandidateSkill;
    Remove: typeof API.removeCandidateSkill;
  };
  Language: {
    Add: typeof API.addCandidateLanguage;
    Edit: typeof API.editCandidateLanguage;
    Remove: typeof API.removeCandidateLanguage;
  };
}

interface CandidateModalStateProps {
  candidate?: CandidateFormData;
  isLoboxUser?: boolean;
  dataMode?: DataMode;
}

interface CandidateModalActionsProps extends CandidateAPIActions {
  setCandidateId: (id: string) => void;
  setDataMode: (dataMode: DataMode) => void;
}

const CreateCandidateModalStateContext =
  createContext<CandidateModalStateProps>({});

const CreateCandidateModalActionsContext =
  createContext<CandidateModalActionsProps>({
    setCandidateId: () => {},
  } as unknown as CandidateModalActionsProps);

const CreateCandidateModalProvider: FC<
  PropsWithChildren<CreateCandidateModalProviderProps>
> = (props) => {
  const { children, initialValue } = props;
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { handleSuccess, handleError } = useResponseToast();
  const [candidateId, setCandidateId] = useState<string | undefined>(
    initialValue?.id
  );
  const [dataMode, setDataMode] = useState<DataMode>(undefined);

  const onAlert = async (
    message: string,
    type: 'success' | 'error',
    title = 'candidate'
  ) => {
    if (type === 'success') {
      handleSuccess({ message: t(message), title: t(title) });
    } else {
      handleError({ message: t(message), title: t('error') });
    }
  };

  const onError = (error: any) => {
    let message = error?.response?.data?.defaultMessage ?? 'Error!';
    if (error?.response?.data?.fieldErrors?.length > 0) {
      message = `${error.response.data.fieldErrors[0].field}: ${error.response.data.fieldErrors[0].defaultMessage}`;
    }

    onAlert(message, 'error');
    throw error;
  };

  const { data, refetch } = useReactQuery<CandidateFormData>({
    action: {
      apiFunc: () => API.getCandidateById(candidateId!),
      key: [QueryKeys.getCandidate, candidateId],
    },
    config: {
      enabled: !!candidateId,
      initialData: initialValue,
    },
  });

  function onMutationSuccess(
    message = 'success_updated',
    title = 'candidate_modified'
  ) {
    return () => {
      onAlert(message, 'success', title);
      refetch();
    };
  }

  const CreateCandidate = wrapApiFunction(
    API.createCandidate,
    (res: CandidateFormData) => {
      setCandidateId(res.id);
      if (!res._isDuplicatdOnCreate) {
        onMutationSuccess('candidate_created_message', 'candidate_created')();
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.getCandidatesList],
          exact: false,
        });
      }
      return res;
    },
    onError
  );

  const EditCandidateBasicInfo = wrapApiFunction(
    API.editCandidateBasic,
    onMutationSuccess('candidate_modified_message'),
    onError
  );

  const EditCandidateSocialInfo = wrapApiFunction(
    API.editCandidateSocialInfo,
    onMutationSuccess('candidate_modified_message'),
    onError
  );

  const EditCandidatePreferenceInfo = wrapApiFunction(
    API.editCandidatePreferenceInfo,
    onMutationSuccess('candidate_modified_message'),
    onError
  );

  const EditCandidateLegalInfo = wrapApiFunction(
    API.editCandidateLegalInfo,
    onMutationSuccess('candidate_modified_message'),
    onError
  );

  const EditCandidateDemographicInfo = wrapApiFunction(
    API.editCandidateDemographicInfo,
    onMutationSuccess('candidate_modified_message'),
    onError
  );

  const EditCandidateAdditionalInfo = wrapApiFunction(
    API.editCandidateAdditionalInfo,
    onMutationSuccess('candidate_modified_message'),
    onError
  );

  const SetLockMode = wrapApiFunction(
    API.setLockMode,
    onMutationSuccess('candidate_modified_message'),
    onError
  );

  const AddExperience = wrapApiFunction(
    API.addCandidateExperience,
    onMutationSuccess(),
    onError
  );

  const EditExperience = wrapApiFunction(
    API.editCandidateExperience,
    onMutationSuccess(),
    onError
  );

  const RemoveExperience = wrapApiFunction(
    API.removeCandidateExperience,
    onMutationSuccess(),
    onError
  );

  const AddEducation = wrapApiFunction(
    API.addCandidateEducation,
    onMutationSuccess(),
    onError
  );

  const EditEducation = wrapApiFunction(
    API.editCandidateEducation,
    onMutationSuccess(),
    onError
  );

  const RemoveEducation = wrapApiFunction(
    API.removeCandidateEducation,
    onMutationSuccess(),
    onError
  );

  const AddSkill = wrapApiFunction(
    API.addCandidateSkill,
    onMutationSuccess(),
    onError
  );

  const EditSkill = wrapApiFunction(
    API.editCandidateSkill,
    onMutationSuccess(),
    onError
  );

  const RemoveSkill = wrapApiFunction(
    API.removeCandidateSkill,
    onMutationSuccess(),
    onError
  );

  const AddLanguage = wrapApiFunction(
    API.addCandidateLanguage,
    onMutationSuccess(),
    onError
  );

  const EditLanguage = wrapApiFunction(
    API.editCandidateLanguage,
    onMutationSuccess(),
    onError
  );

  const RemoveLanguage = wrapApiFunction(
    API.removeCandidateLanguage,
    onMutationSuccess(),
    onError
  );

  useEffect(() => {
    if (initialValue?.id) {
      setCandidateId(initialValue?.id);
    }
  }, [initialValue]);

  const mutationsRef = useRef<CandidateAPIActions>({
    Candidate: {
      Create: CreateCandidate,
      EditBasic: EditCandidateBasicInfo,
      EditSocial: EditCandidateSocialInfo,
      EditPreference: EditCandidatePreferenceInfo,
      EditLegal: EditCandidateLegalInfo,
      EditDemographic: EditCandidateDemographicInfo,
      EditAdditional: EditCandidateAdditionalInfo,
      SetLockMode: SetLockMode,
    },
    Experience: {
      Add: AddExperience,
      Edit: EditExperience,
      Remove: RemoveExperience,
    },
    Education: {
      Add: AddEducation,
      Edit: EditEducation,
      Remove: RemoveEducation,
    },
    Skill: {
      Add: AddSkill,
      Edit: EditSkill,
      Remove: RemoveSkill,
    },
    Language: {
      Add: AddLanguage,
      Edit: EditLanguage,
      Remove: RemoveLanguage,
    },
  });

  const isLoboxUser = useMemo(() => !!data?.profile?.username, [data]);

  const memoizedStateValue = useMemo<CandidateModalStateProps>(
    () => ({
      candidate: candidateId ? data : initialValue,
      dataMode,
      isLoboxUser,
    }),
    [data, candidateId, initialValue, isLoboxUser, dataMode]
  );

  const memoizedActionsValue = useMemo<CandidateModalActionsProps>(
    () => ({
      setCandidateId,
      setDataMode,
      ...mutationsRef.current,
    }),
    [setCandidateId, setDataMode]
  );

  return (
    <CreateCandidateModalStateContext.Provider value={memoizedStateValue}>
      <CreateCandidateModalActionsContext.Provider value={memoizedActionsValue}>
        {children}
      </CreateCandidateModalActionsContext.Provider>
    </CreateCandidateModalStateContext.Provider>
  );
};

export const useCandidateModalState = () =>
  useContext(CreateCandidateModalStateContext);
export const useCandidateModalActions = () =>
  useContext(CreateCandidateModalActionsContext);

export const useCandidateModalContext = () => {
  const state = useCandidateModalState();
  const actions = useCandidateModalActions();

  return {
    ...state,
    ...actions,
  };
};

export default CreateCandidateModalProvider;
