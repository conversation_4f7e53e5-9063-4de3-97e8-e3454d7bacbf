@import '/src/shared/theme/theme.scss';

@layer organism {
  .stepWrapper {
    display: flex;
    flex-direction: column;
    gap: variables(gutter);
    width: 100%;
    flex-grow: 1;
  }
  .formBuilder {
    flex-direction: column;
    gap: variables(xLargeGutter) * 0.5;
    flex-wrap: nowrap;
    width: 100%;
    flex-grow: 1;
  }
  .halfWidthWrapperStyle {
    width: calc(50% - #{variables(gutter)} * 0.25);
  }
  .formGroupLabel,
  .firstFormGroupLabel {
    margin: 0;
  }
  .formGroupLabel,
  .lgFormGroupLabel {
    margin-top: variables(gutter) * 0.5;
  }
  .addBtn {
    margin-top: variables(gutter) * 0.25;
  }
  .listContainer {
    gap: variables(xLargeGutter) * 0.5;
  }
  .listGroupHeader {
    margin-bottom: 0;
  }
  .footer {
    flex-direction: row;
    gap: variables(gutter) * 0.5;
    width: 100%;
  }
  .formSection {
    margin-top: variables(xLarge<PERSON>utter) * 0.5;
    gap: variables(xLarge<PERSON>utter) * 0.5;
  }
  .formRoot {
    flex-direction: column;
    flex: 1;
    gap: variables(gutter) * 0.5;
  }
  @media (min-width: breakpoints(tablet)) {
    .formRoot {
      gap: variables(xLargeGutter) * 0.5;
    }
  }
  .menuItemRoot {
    gap: variables(xLargeGutter);
    padding-top: variables(gutter);
  }
  .menuItem {
    padding: 0;
  }
  .resumePickerWrapper {
    border: 1px dashed colors(techGray_20);
    gap: variables(gutter) * 0.5;
    border-radius: variables(gutter) * 0.25;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .uploadedResumeCard {
    background: colors(background);
    padding: variables(gutter);
    border-radius: variables(xLargeGutter) * 0.5;
    border: 1px solid colors(techGray_20);
  }
  .addonIcon {
    padding: variables(gutter) * 0.25;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .inputAddon {
    background-color: colors(darkSecondary_hover);
    min-width: 220px;
  }
  .formItemWrapStyle {
    flex-basis: content !important;
  }
  .infoBoxesWrapper {
    gap: variables(gutter) * 0.5;
    overflow: hidden;
    .updateCandidateInfoBox {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: start;
      gap: variables(gutter) * 0.5;
      z-index: 1001;
      .buttons {
        width: 100%;
        justify-content: center;
        gap: 6px;
      }
    }
    .candidateExistsInfoBox {
      z-index: 1001;
      background-color: colors(warning_10);
    }
  }
  .disablesection::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    background-color: colors(popOverBg_white);
    opacity: 0.5;
    overflow-y: auto;
    width: 100%;
    height: 100%;
    z-index: 1000;
  }

  .selectedDataInfoBox {
    background-color: colors(success_10) !important;
    padding: variables(gutter) !important;
  }
}
