import Info from '@shared/components/molecules/Info/Info';
import useTranslation from '@shared/utils/hooks/useTranslation';
import classes from '../../CreateCandidate.module.scss';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import { DataMode } from '@shared/components/Organism/MultiStepForm/CreateCandidateForm/CreateCandidateModalProvider';

const CandidateOrCustomDataSelection = ({
  onSelect,
  dataMode,
}: {
  onSelect: (dataMode: DataMode) => void;
  dataMode: DataMode;
}) => {
  const { t } = useTranslation();
  return (
    <Flex className={classes.infoBoxesWrapper}>
      {!dataMode ? (
        <>
          <Info
            text={t('candidate_exists__add_more')}
            textColor="secondaryText"
            color="warning"
            icon="exclamation-triangle"
            className={classes.candidateExistsInfoBox}
          />
          <Info
            text={t('choose_to_continue_with_candidate_data')}
            textColor="secondaryDisabledText"
            color="gray_50"
            icon="exclamation-circle"
            className={classes.updateCandidateInfoBox}
            otherComponents={
              <Flex
                flexDir="row"
                alignItems="center"
                className={classes.buttons}
              >
                <Button
                  schema="semi-transparent"
                  label={t('use_candidate_data')}
                  fullWidth
                  onClick={() => onSelect('candidate')}
                />
                <Button
                  label={t('use_custom_data')}
                  fullWidth
                  onClick={() => onSelect('custom')}
                />
              </Flex>
            }
          />
        </>
      ) : (
        <SelectedDataInfoBox
          dataMode={dataMode}
          onClick={() => onSelect(undefined)}
        />
      )}
    </Flex>
  );
};

const SelectedDataInfoBox = ({
  dataMode,
  onClick,
}: {
  dataMode: DataMode;
  onClick: () => void;
}) => {
  const { t } = useTranslation();
  return (
    <Info
      text={
        dataMode === 'candidate'
          ? t('choosed_candidate_data')
          : t('choosed_custom_data')
      }
      textColor="success"
      color="success"
      className={classes.selectedDataInfoBox}
      icon="check"
      otherComponents={
        <Button
          label={t('change')}
          labelColor="primaryText"
          variant="text"
          onClick={onClick}
        />
      }
    />
  );
};

export default CandidateOrCustomDataSelection;
