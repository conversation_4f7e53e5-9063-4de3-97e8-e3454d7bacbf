'use client';

import React, { useState } from 'react';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import getStepData from 'shared/utils/getStepData';
import MultiStepForm from '../MultiStepForm';
import { useSubmitJobStepOne } from './useSubmitJobStepOne';
import { useSubmitJobStepTwo } from './useSubmitJobStepTwo';

const SubmitJob: React.FC = () => {
  const { data: submitJob } = useMultiStepFormState('submitJob');
  const initialStep = submitJob?.step === '1' ? 0 : 1;

  const data = useSubmitJobStepOne();
  const sendStep = useSubmitJobStepTwo();
  const steps = [...data, ...sendStep];

  const onClose = () => closeMultiStepForm('submitJob');
  const getHeaderProps = getStepData('getHeaderProps', steps);
  const getStepHeaderProps = getStepData('getStepHeaderProps', steps);
  const renderBody = getStepData('renderBody', steps);
  const renderFooter = getStepData('renderFooter', steps);

  const initialValues = {
    jobId: '',
    isSubmitted: false,
  };

  return (
    <MultiStepForm
      wide
      initialStep={initialStep}
      showConfirm={false}
      enableReinitialize
      initialValues={initialValues}
      totalSteps={steps?.length}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      isOpenAnimation
      formName="submitJob"
    />
  );
};

export default SubmitJob;
