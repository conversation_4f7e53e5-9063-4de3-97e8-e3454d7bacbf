'use client';

import React, { useState } from 'react';
import { useNoteBulkAction } from '@shared/components/Organism/BulkActions/hooks/useNoteBulkAction';
import { useTodoBulkAction } from '@shared/components/Organism/BulkActions/hooks/useTodoBulkAction';
import {
  CANDIDATE_NOTE_MAX_LENGTH,
  DESCRIPTION_MAX_LENGTH,
} from '@shared/constants/enums';
import useTranslation from '@shared/utils/hooks/useTranslation';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import MultiStepForm from '../MultiStepForm';
import { usePipelineBulkActionsStepFour } from './usePipelineBulkActionsStepFour';
import { usePipelineBulkActionsStepThree } from './usePipelineBulkActionsStepThree';
import { usePipelineBulkActionsStepTwo } from './usePipelineBulkActionsStepTwo';
import { usePipelineBulkActionStepOne } from './usePipelineBulkActionStepOne';
import { useGetApiPartials } from './utils';
import type { PipelineItem } from './usePipelineBulkActionsStepTwo';
import type { MultiStepFormProps } from '../MultiStepForm';

export type Method = 'google' | 'emails' | 'bulk' | undefined;

const PipelineBulkAction: React.FC = () => {
  const { t } = useTranslation();
  const [pipelineItem, setPipelineItem] = useState<PipelineItem>({
    label: undefined,
    value: undefined,
  });
  const [timesheetId, setTimesheetId] = useState<number | undefined>(undefined);
  const { data: pipelineBulkAction } =
    useMultiStepFormState('pipelineBulkAction');
  const apiPartials = useGetApiPartials({ pipelineItem, timesheetId });

  const initialStep = pipelineBulkAction?.step === '1' ? 0 : 1;
  const [currentStep, setCurrentStep] = useState(1);

  const step1 = usePipelineBulkActionStepOne({ setCurrentStep });
  const step2 = usePipelineBulkActionsStepTwo({
    setPipelineItem,
    pipelineItem,
  });
  const step3 = usePipelineBulkActionsStepThree({
    timesheetId,
    setTimesheetId,
  });
  const step4 = usePipelineBulkActionsStepFour();
  const step5 = useNoteBulkAction({
    backStep: 0,
    cancelLabel: t('discard'),
    submitLabel: t('save'),
    descriptionMaxLength: CANDIDATE_NOTE_MAX_LENGTH,
    headerTitle: t('note'),
    stepKey: '3',
  });
  const step6 = useTodoBulkAction({
    backStep: 0,
    cancelLabel: t('discard'),
    descriptionMaxLength: DESCRIPTION_MAX_LENGTH,
    headerTitle: t('todo'),
    stepKey: '4',
    submitLabel: t('save'),
  });

  const steps = [...step1, ...step2, ...step3, ...step4, ...step5, ...step6];

  const onClose = () => closeMultiStepForm('pipelineBulkAction');

  const getHeaderProps = getStepData('getHeaderProps', steps);
  const getStepHeaderProps = getStepData('getStepHeaderProps', steps);
  const renderBody = getStepData('renderBody', steps);
  const renderFooter = getStepData('renderFooter', steps);

  return (
    <MultiStepForm
      wide
      initialStep={initialStep}
      apiFunc={apiPartials?.[currentStep]?.apiFunc}
      onSuccess={apiPartials?.[currentStep]?.onSuccess}
      initialValues={apiPartials?.[currentStep]?.initialValues}
      transform={apiPartials?.[currentStep]?.transform}
      showConfirm={false}
      enableReinitialize
      totalSteps={steps?.length}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      isOpenAnimation
      formName="pipelineBulkAction"
    />
  );
};

export default PipelineBulkAction;

function getStepData<T extends keyof MultiStepFormProps>(
  key: T,
  data: MultiStepFormProps[]
): MultiStepFormProps[T] {
  return ({ step, ...rest }: any) => data?.[step]?.[key]?.({ step, ...rest });
}
