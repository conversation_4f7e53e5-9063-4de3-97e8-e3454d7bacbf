import { useParams } from 'next/navigation';
import { useState } from 'react';
import { useTemplateBulkAction } from '@shared/components/Organism/BulkActions/hooks/useTemplateBulkAction';
import { CANDIDATE_NOTE_MAX_LENGTH } from '@shared/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { MultiStepFormProps } from '../MultiStepForm';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

export function usePipelineBulkActionsStepFour(): SingleDataItem[] {
  const { t } = useTranslation();
  const params = useParams();

  const [formData, setFormData] = useState<{
    item: any;
    subject: string;
    message: string;
    fieIds: number[];
  } | null>(null);

  const moveToStep = useTemplateBulkAction({
    cancelLabel: t('discard'),
    headerLabel: t('email'),
    item: formData?.item,
    onChange: (item) => {
      setFormData({
        item: { value: item.id, label: item.title },
        subject: item?.subject,
        message: item?.message,
        fieIds: item?.fileIds,
      });
    },

    disabledSubmit: !formData?.item?.value,
    stepKey: '4',
    submitLabel: t('send'),
    backStep: 0,
    additionalFields: [
      {
        name: 'subject',
        cp: 'input',
        label: t('subject'),
        required: false,
        disabled: true,
        readOnly: true,
        value: formData?.subject,
        visibleOptionalLabel: false,
        disabledReadOnly: true,
      },
      {
        name: 'message',
        cp: 'richtext',
        label: t(`message`),
        showEmoji: false,
        className: '!h-[252px] overflow-auto',
        helperText: t('dynamic_template_helper_text'),
        disabled: true,
        required: false,
        readonly: true,
        visibleOptionalLabel: false,
        value: formData?.message,
        defaultValue: formData?.message,
        maxLength: CANDIDATE_NOTE_MAX_LENGTH,
        disabledReadOnly: true,
      },
      {
        name: 'attachmentFileIds',
        cp: 'attachmentPicker',
        value: formData?.fieIds,
        wrapStyle: 'pointer-events-none',
        label: t('attachment'),
        required: false,
        visibleOptionalLabel: false,
        disabledReadOnly: true,
      },
    ],
  });

  return moveToStep;
}
