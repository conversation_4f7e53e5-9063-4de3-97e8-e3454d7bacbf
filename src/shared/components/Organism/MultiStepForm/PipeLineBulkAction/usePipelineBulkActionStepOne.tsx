import { closeMultiStepForm } from '@shared/hooks/useMultiStepForm';
import BaseButton from '@shared/uikit/Button/BaseButton';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { MultiStepFormProps } from '../MultiStepForm';
import type { IconName } from '@shared/uikit/Icon/types';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
};

type PipelineBulkActionStepOneProps = {
  setCurrentStep: (value: number) => void;
};

export function usePipelineBulkActionStepOne({
  setCurrentStep,
}: PipelineBulkActionStepOneProps): SingleDataItem[] {
  const { t } = useTranslation();

  const pipelineBulkActions = [
    {
      step: 1,
      icon: 'Pipe-move' as IconName,
      title: t('move_to'),
    },
    {
      step: 2,
      icon: 'meeting' as IconName,
      title: t('interview'),
    },
    {
      step: 3,
      icon: 'envelope' as IconName,
      title: t('email'),
    },
    {
      step: 4,
      icon: 'note' as IconName,
      title: t('note'),
    },
    {
      step: 5,
      icon: 'checklist' as IconName,
      title: t('todo'),
    },
  ];

  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title: t('set_bulk_actions'),
    closeButtonProps: {
      onClick: () => {
        closeMultiStepForm('pipelineBulkAction');
      },
    },
  });

  const data: Array<SingleDataItem> = [
    {
      stepKey: '1',
      getHeaderProps,

      renderBody: ({ setStep }) => (
        <Flex className="gap-2">
          {pipelineBulkActions.map((item, index) => (
            <BaseButton
              onClick={() => {
                setStep(item?.step);
                setCurrentStep(item?.step);
              }}
              key={index}
            >
              <Flex
                flexDir="row"
                className="items-center justify-between hover:bg-hoverPrimary rounded p-8"
              >
                <Flex flexDir="row" className="gap-8 items-center">
                  <IconButton
                    name={item.icon}
                    type="far"
                    size="xl40"
                    colorSchema="backgroundIconSecondary"
                    variant="rectangle"
                  />
                  <Typography fontSize={15} fontWeight={700} color="smoke_coal">
                    {item.title}
                  </Typography>
                </Flex>
                <Icon color="white" name="chevron-right" type="fas" size={20} />
              </Flex>
            </BaseButton>
          ))}
        </Flex>
      ),
    },
  ];

  return data;
}
