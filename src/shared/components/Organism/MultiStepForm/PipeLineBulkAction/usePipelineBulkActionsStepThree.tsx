import { useParams } from 'next/navigation';
import { useState } from 'react';
import { useTemplateBulkAction } from '@shared/components/Organism/BulkActions/hooks/useTemplateBulkAction';
import { CANDIDATE_NOTE_MAX_LENGTH } from '@shared/constants/enums';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import AsyncAutoComplete from '@shared/uikit/AutoComplete/AsyncAutoComplete';
import { schedulesEndpoints } from '@shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { MultiStepFormProps } from '../MultiStepForm';
import { getAllMeetingTemplates } from '@shared/utils/api/template';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type PipelineBulkActionsStepTwo = {
  timesheetId?: number;
  setTimesheetId: React.Dispatch<React.SetStateAction<number | undefined>>;
};

export function usePipelineBulkActionsStepThree({
  timesheetId,
  setTimesheetId,
}: PipelineBulkActionsStepTwo): SingleDataItem[] {
  const { t } = useTranslation();
  const params = useParams();
  const jobId = params?.id as string;
  const { authUser } = useGetAppObject();
  const userId = authUser?.id;

  const [formData, setFormData] = useState<{
    item: any;
    subject: string;
    message: string;
    fieIds: number[];
  } | null>(null);

  const moveToStep = useTemplateBulkAction({
    cancelLabel: t('discard'),
    headerLabel: t('interview'),
    item: formData?.item,
    apiFunc: getAllMeetingTemplates,
    normalizer: (data: any) =>
      data?.content?.content?.map((item: any) => ({
        value: item.id,
        label: item.title,
        ...item,
      })),
    onChange: (item) => {
      setFormData({
        item: { value: item.id, label: item.title },
        subject: item?.subject,
        message: item?.message,
        fieIds: item?.fileIds,
      });
    },
    introElement: (
      <AsyncAutoComplete
        variant="simple-large"
        label={t('availability')}
        name="availability"
        placeholder={t('availability')}
        rightIconProps={{ name: 'chevron-down', size: 'md18' }}
        visibleRightIcon
        onChange={(val: any) => setTimesheetId(val?.value)}
        url={schedulesEndpoints.getUserTimesheet(userId || '')}
        normalizer={(res: any) =>
          res?.timesheets?.map((item: any) => ({
            value: item?.id,
            label: item?.title,
          }))
        }
      />
    ),
    disabledSubmit: !timesheetId && !formData?.item?.value,
    stepKey: '2',
    submitLabel: t('send'),
    backStep: 0,
    additionalFields: [
      {
        name: 'subject',
        cp: 'input',
        label: t('subject'),
        required: false,
        disabled: true,
        readOnly: true,
        value: formData?.subject,
        visibleOptionalLabel: false,
        disabledReadOnly: true,
      },
      {
        name: 'message',
        cp: 'richtext',
        label: t(`message`),
        showEmoji: false,
        className: '!h-[252px] overflow-auto',
        helperText: t('dynamic_template_helper_text'),
        disabled: true,
        required: false,
        readonly: true,
        visibleOptionalLabel: false,
        value: formData?.message,
        defaultValue: formData?.message,
        maxLength: CANDIDATE_NOTE_MAX_LENGTH,
        disabledReadOnly: true,
      },
      {
        name: 'attachmentFileIds',
        cp: 'attachmentPicker',
        value: formData?.fieIds,
        wrapStyle: 'pointer-events-none',
        label: t('attachment'),
        required: false,
        visibleOptionalLabel: false,
        disabledReadOnly: true,
      },
    ],
  });

  return moveToStep;
}
