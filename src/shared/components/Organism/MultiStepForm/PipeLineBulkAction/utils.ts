import { useQueryClient } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import useResponseToast from '@shared/hooks/useResponseToast';
import {
  pipelineBulkActionEmail,
  pipelineBulkActionMeeting,
  pipelineBulkActionMoveTo,
  pipelineBulkActionNote,
  pipelineBulkActionTodo,
} from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { PipelineItem } from './usePipelineBulkActionsStepTwo';

type GetApiPartials = {
  pipelineItem?: PipelineItem;
  timesheetId?: number;
};

export const useGetApiPartials = ({
  pipelineItem,
  timesheetId,
}: GetApiPartials) => {
  const { t } = useTranslation();
  const { handleSuccess } = useResponseToast();
  const { data: pipelineBulkAction } =
    useMultiStepFormState('pipelineBulkAction');
  const queryClient = useQueryClient();
  const params = useParams();
  const jobId = params?.id;

  const updatePipelines = () => {
    queryClient.invalidateQueries({
      queryKey: [QueryKeys.getPipeline, jobId],
    });
  };

  const apiPartials: Record<number, any> = {
    1: {
      apiFunc: pipelineBulkActionMoveTo,
      onSuccess: () => {
        handleSuccess({
          message: `${t('candidate_bulk_moved_message')} ${pipelineItem?.label}`,
          title: t('candidate_bulk_moved_title'),
        });
        updatePipelines();
      },
      transform: (formValues: any) => ({
        templateId: formValues?.template?.value,
        pipelineId: pipelineItem?.value,
        participationIds: (pipelineBulkAction?.participationIds || []).map(
          (id: any) => Number(id)
        ),
      }),
      initialValues: {
        template: '',
        subject: '',
        message: '',
        attachmentFileIds: null,
      },
    },
    2: {
      apiFunc: pipelineBulkActionMeeting,
      onSuccess: () => {
        handleSuccess({
          message: `${t('candidate_bulk_meeting_message')}`,
          title: t('candidate_bulk_meeting_title'),
        });
        updatePipelines();
      },
      transform: (formValues: any) => ({
        templateId: formValues?.template?.value,
        timesheetId,
        participationIds: (pipelineBulkAction?.participationIds || []).map(
          (id: any) => Number(id)
        ),
      }),
      initialValues: {
        template: '',
        subject: '',
        message: '',
        attachmentFileIds: null,
      },
    },
    3: {
      apiFunc: pipelineBulkActionEmail,
      onSuccess: () => {
        handleSuccess({
          message: `${t('candidate_bulk_email_message')}`,
          title: t('candidate_bulk_email_title'),
        });
        updatePipelines();
      },
      transform: (formValues: any) => ({
        templateId: formValues?.template?.value,
        participationIds: (pipelineBulkAction?.participationIds || []).map(
          (id: any) => Number(id)
        ),
      }),
      initialValues: {
        template: '',
        subject: '',
        message: '',
        attachmentFileIds: null,
      },
    },
    4: {
      apiFunc: pipelineBulkActionNote,
      onSuccess: () => {
        handleSuccess({
          message: t('candidate_bulk_note_message'),
          title: t('candidate_bulk_note_title'),
        });
        updatePipelines();
      },
      transform: (formValues: any) => ({
        body: formValues?.body,
        visibility: formValues?.visibility || 'EVERYONE',
        fileIds: formValues?.fileIds?.map((item: any) => item?.id),
        participationIds: (pipelineBulkAction?.participationIds || []).map(
          (id: any) => Number(id)
        ),
      }),
      initialValues: {
        body: '',
        visibility: '',
        fileIds: null,
      },
    },
    5: {
      apiFunc: pipelineBulkActionTodo,
      onSuccess: () => {
        handleSuccess({
          message: t('candidate_bulk_todo_message'),
          title: t('candidate_bulk_todo_title'),
        });
        updatePipelines();
      },
      transform: (formValues: any) => {
        const [startDate] = formValues.startDate?.split('T') ?? [];
        const startTime = formValues.startTime?.value || '00:00';
        const [endDate] = formValues.endDate?.split('T') ?? [];
        const endTime = formValues.endTime?.value || '00:00';

        return {
          title: formValues?.title,
          description: formValues?.description,
          assigneeUserId: formValues?.assigneeUserId?.value,
          allTeamMembersTagged: formValues?.allTeamMembersTagged,
          start: startDate ? `${startDate}T${startTime}` : '',
          end: endDate ? `${endDate}T${endTime}` : '',
          remind: formValues?.remind,
          fileIds: formValues?.fileIds?.map((item: any) => item?.id),
          participationIds: (pipelineBulkAction?.participationIds || []).map(
            (id: any) => Number(id)
          ),
        };
      },
      initialValues: {
        fileIds: null,
        title: '',
        description: '',
        assigneeUserId: '',
        start: '',
        end: '',
        remind: true,
      },
    },
  };

  return apiPartials;
};
