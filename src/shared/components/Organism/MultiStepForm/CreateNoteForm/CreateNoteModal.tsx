import React, { useMemo } from 'react';
import useResponseToast from '@shared/hooks/useResponseToast';
import { closeMultiStepForm } from 'shared/hooks/useMultiStepForm';
import getStepData from 'shared/utils/getStepData';
import useTranslation from 'shared/utils/hooks/useTranslation';
import MultiStepForm from '../MultiStepForm';
import useCreateTodoForm from './useCreateNoteForm';
import { batchAddCandidateNote } from '@shared/utils/api/candidates';
import { getNoteValidationSchema } from '@shared/components/Organism/CandidateNoteCard/CandidateNoteCard.component';
import { UpdateNoteFormFields } from '@shared/types/note';
import { CandidateNoteRequest } from '@shared/types/candidates';

const CreateNoteModal = ({ data }: { data: any }) => {
  const { t } = useTranslation();
  const onClose = () => closeMultiStepForm('createNoteForm');
  const { data: formData } = useCreateTodoForm(data?.target ?? 'list');
  const totalSteps = useMemo(() => formData.length ?? 0, [formData]);
  const { handleSuccess, handleError } = useResponseToast();
  const getHeaderProps = getStepData('getHeaderProps', formData);
  const renderFooter = getStepData('renderFooter', formData);
  const renderBody = getStepData('renderBody', formData);

  const onAlert = async (message: string, type: 'success' | 'error') => {
    if (type === 'success') {
      handleSuccess({
        message,
      });
    } else {
      handleError({ message });
    }
    onClose();
  };

  const onSuccess = () => {
    onAlert(t('note_added_to_selected_candidates'), 'success');
  };

  return (
    <MultiStepForm
      apiFunc={batchAddCandidateNote}
      getValidationSchema={getNoteValidationSchema}
      totalSteps={totalSteps}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      onClose={onClose}
      onSuccess={onSuccess}
      onFailure={(error) =>
        onAlert(error.message ?? error.defaultMessage ?? 'Error!', 'error')
      }
      enableReinitialize
      formName="createNoteForm"
      isOpenAnimation
      transform={(values: any) => {
        return {
          ...transformCandidateNote(values),
          candidateIds: values?.selectedCandidates,
        };
      }}
      initialValues={{
        selectedCandidates: [],
        visibility: 'TEAM',
        body: '',
        fileIds: [],
      }}
    />
  );
};

export default CreateNoteModal;

function transformCandidateNote(
  note: UpdateNoteFormFields
): CandidateNoteRequest {
  return {
    body: note.body,
    visibility: note.visibility,
    fileIds: note.fileIds ? note.fileIds : undefined,
  };
}
