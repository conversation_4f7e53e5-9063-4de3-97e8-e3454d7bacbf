import { closeMultiStepForm } from 'shared/hooks/useMultiStepForm';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { Dispatch, SetStateAction } from 'react';
import CreateNoteModalFooter from './CreateNoteModal/CreateNoteModalFooter';
import { MultiStepFormStepProps } from '@shared/types/formTypes';
import CreateNoteStepOne from './CreateNoteModal/CreateNoteModalBody/CreateNoteStepOne';
import CreateNoteStepTwo from './CreateNoteModal/CreateNoteModalBody/CreateNoteStepTwo';

export default function useCreateNoteForm(target: 'list' | 'form') {
  const { t } = useTranslation();

  const listApiParams = { onlyCandidates: true };

  const getHeaderProps = ({
    setStep,
    step,
  }: {
    setStep: Dispatch<SetStateAction<number>>;
    step: number;
  }) => ({
    title: t('create_note'),
    hideBack: false,
    backButtonProps: {
      onClick: () => {
        if (step === 0) closeMultiStepForm('createNoteForm');
        setStep((prev) => prev - 1);
      },
    },
    noCloseButton: true,
  });
  const renderFooter = (props: any) => {
    return <CreateNoteModalFooter {...props} isSubmitStep={props.step === 1} />;
  };

  const renderBodyStepOne = () => {
    return <CreateNoteStepOne />;
  };

  const renderBodyStepTwo = () => {
    return <CreateNoteStepTwo />;
  };

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] =
    () => {
      return {
        ...data,
        title: t('create_note'),
      };
    };

  const onClickCandidate = (item: any, values: any, setFieldValue: any) => {
    const index = values.selectedCandidates?.findIndex(
      (id: number) => id === item.id
    );
    if (index === -1) {
      setFieldValue('selectedCandidates', [
        ...(values.selectedCandidates || []),
        item.id,
      ]);
    } else {
      setFieldValue(
        'selectedCandidates',
        values.selectedCandidates?.filter((id: number) => id !== item.id) || []
      );
    }
  };

  const data: Array<MultiStepFormStepProps> = [
    {
      stepKey: 'list',
      getStepHeaderProps,
      renderFooter,
      renderBody: renderBodyStepOne,
      getHeaderProps,
    },
    {
      stepKey: 'form',
      getStepHeaderProps,
      renderFooter,
      renderBody: renderBodyStepTwo,
      getHeaderProps,
    },
  ];

  return { data, listApiParams, onClickCandidate };
}
