import useTranslation from '@shared/utils/hooks/useTranslation';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import { useMemo } from 'react';
import { useNoteFields } from '@shared/components/Organism/CandidateNoteCard/hooks/useNoteFields';
import { noteVisibilityOptions } from '@shared/utils/constants/enums/candidateDb';

const CreateNoteStepTwo = () => {
  const { t } = useTranslation();
  const fields = useNoteFields();

  const VISIBILITY_RADIO = useMemo(
    () => ({
      formGroup: {
        color: 'border',
        title: t('visibility'),
        className: '!p-0 !m-0',
        titleProps: {
          font: '400',
          size: 15,
        },
      },
      name: 'visibility',
      cp: 'radioGroup',
      visibleOptionalLabel: false,
      required: true,
      options: noteVisibilityOptions,
      classNames: {
        root: 'flex !flex-row gap-20',
        itemWrapper: '!mb-4',
      },
      className: '!ps-0 !pb-0',
    }),
    [t]
  );

  const groups = useMemo(
    () => [
      VISIBILITY_RADIO,
      { ...fields.BODY, label: t('write_note') },
      {
        ...fields.ATTACHMENT,
        classNames: {
          dropzoneOvveride: undefined,
        },
        showUploadList: true,
        buttonComponent: undefined,
      },
    ],
    [fields]
  );

  return <DynamicFormBuilder groups={groups} className="gap-12" />;
};

export default CreateNoteStepTwo;
