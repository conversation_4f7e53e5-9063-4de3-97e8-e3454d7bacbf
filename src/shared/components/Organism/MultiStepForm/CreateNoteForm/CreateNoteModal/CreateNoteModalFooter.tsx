import type { FC } from 'react';
import Flex from 'shared/uikit/Flex';
import Button from 'shared/uikit/Button';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import eventKeys from 'shared/constants/event-keys';
import Observer from '@shared/components/atoms/Observer';
import type { CallbackParams } from '../../MultiStepForm';
import { useFormikContext } from 'formik';

type CreateNoteModalFooterProps = Pick<
  CallbackParams,
  | 'isSubmitting'
  | 'setStep'
  | 'isValid'
  | 'status'
  | 'validateForm'
  | 'step'
  | 'values'
> & {
  isSubmitStep?: boolean;
  isEdit?: boolean;
  isLoading?: boolean;
};

const CreateNoteModalFooter: FC<CreateNoteModalFooterProps> = ({
  isSubmitStep,
  setStep,
  step,
  validateForm,
}) => {
  const { t } = useTranslation();
  const { values } = useFormikContext<{ selectedCandidates: number[] }>();
  const selectedCandidates = values?.selectedCandidates;

  return (
    <Flex>
      <Observer step={step} validateForm={validateForm} />
      <Flex flexDir="row" className="gap-12 justify-between">
        <Button
          fullWidth
          label={t('discard')}
          schema="gray-semi-transparent"
          onClick={() => event.trigger(eventKeys.closeModal)}
        />
        {isSubmitStep ? (
          <SubmitButton fullWidth label={t('create')} schema="primary-blue" />
        ) : (
          <Button
            fullWidth
            label={t('next')}
            schema="primary-blue"
            onClick={() => setStep((prev) => prev + 1)}
            disabled={!selectedCandidates.length}
          />
        )}
      </Flex>
    </Flex>
  );
};

export default CreateNoteModalFooter;
