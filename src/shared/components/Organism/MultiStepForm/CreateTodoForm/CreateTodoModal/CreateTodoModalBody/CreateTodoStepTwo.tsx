import useTranslation from '@shared/utils/hooks/useTranslation';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import { useMemo } from 'react';
import { useTodoFields } from '@shared/components/Organism/CandidateTodoCard/hooks/useTodoFields';

const CreateTodoStepTwo = () => {
  const { t } = useTranslation();
  const fields = useTodoFields();
  const groups = useMemo(
    () => [
      { ...fields.ASSIGNEE, required: true, forceVisibleError: false },
      {
        ...fields.TITLE,
        label: t('title'),
        labelProps: undefined,
      },
      { ...fields.DESCRIPTION, maxLength: 3000 },
      {
        ...fields.ATTACHMENT,
        classNames: {
          dropzoneOvveride: undefined,
        },
        showUploadList: true,
        label: t('attachment'),
      },
      fields.START_DATE,
      fields.START_TIME,
      fields.END_DATE,
      fields.END_TIME,
    ],
    [fields]
  );

  return <DynamicFormBuilder groups={groups} className="gap-12" />;
};

export default CreateTodoStepTwo;
