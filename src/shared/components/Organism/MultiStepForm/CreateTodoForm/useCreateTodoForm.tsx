import { closeMultiStepForm } from 'shared/hooks/useMultiStepForm';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { Dispatch, SetStateAction } from 'react';
import CreateTodoModalFooter from './CreateTodoModal/CreateTodoModalFooter';
import { MultiStepFormStepProps } from '@shared/types/formTypes';
import CreateTodoStepOne from './CreateTodoModal/CreateTodoModalBody/CreateTodoStepOne';
import CreateTodoStepTwo from './CreateTodoModal/CreateTodoModalBody/CreateTodoStepTwo';

export default function useCreateTodoForm(target: 'list' | 'form') {
  const { t } = useTranslation();

  const listApiParams = { onlyCandidates: true };

  const getHeaderProps = ({
    setStep,
    step,
  }: {
    setStep: Dispatch<SetStateAction<number>>;
    step: number;
  }) => ({
    title: t('create_todo'),
    hideBack: false,
    backButtonProps: {
      onClick: () => {
        if (step === 0) closeMultiStepForm('createTodoForm');
        setStep((prev) => prev - 1);
      },
    },
    noCloseButton: true,
  });
  const renderFooter = (props: any) => {
    return <CreateTodoModalFooter {...props} isSubmitStep={props.step === 1} />;
  };

  const renderBodyStepOne = () => {
    return <CreateTodoStepOne />;
  };

  const renderBodyStepTwo = () => {
    return <CreateTodoStepTwo />;
  };

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] =
    () => {
      return {
        ...data,
      };
    };

  const onClickCandidate = (item: any, values: any, setFieldValue: any) => {
    const index = values.selectedCandidates?.findIndex(
      (id: number) => id === item.id
    );
    if (index === -1) {
      setFieldValue('selectedCandidates', [
        ...(values.selectedCandidates || []),
        item.id,
      ]);
    } else {
      setFieldValue(
        'selectedCandidates',
        values.selectedCandidates?.filter((id: number) => id !== item.id) || []
      );
    }
  };

  const data: Array<MultiStepFormStepProps> = [
    {
      stepKey: 'list',
      getStepHeaderProps,
      renderFooter,
      renderBody: renderBodyStepOne,
      getHeaderProps,
    },
    {
      stepKey: 'form',
      getStepHeaderProps,
      renderFooter,
      renderBody: renderBodyStepTwo,
      getHeaderProps,
    },
  ];

  return { data, listApiParams, onClickCandidate };
}
