import React, { useMemo } from 'react';
import useResponseToast from '@shared/hooks/useResponseToast';
import { closeMultiStepForm } from 'shared/hooks/useMultiStepForm';
import getStepData from 'shared/utils/getStepData';
import useTranslation from 'shared/utils/hooks/useTranslation';
import MultiStepForm from '../MultiStepForm';
import useCreateTodoForm from './useCreateTodoForm';
import { batchAddCandidateTodo } from '@shared/utils/api/candidates';
import { newCandidateTodoTransform } from '@shared/components/Organism/CandidateManager/tabs/tab2.todos/CandidateTodosManager';
import { getTodoValidationSchema } from '@shared/components/Organism/CandidateTodoCard/CandidateTodoCard.component';
import { datesPopperTransformer } from '@shared/components/Organism/CandidateTodoCard/hooks/useTodoFields';

const CreateTodoModal = ({ data }: { data: any }) => {
  const { t } = useTranslation();
  const onClose = () => closeMultiStepForm('createTodoForm');
  const { data: formData } = useCreateTodoForm(data?.target ?? 'list');
  const totalSteps = useMemo(() => formData.length ?? 0, [formData]);
  const { handleSuccess, handleError } = useResponseToast();
  const getHeaderProps = getStepData('getHeaderProps', formData);
  const getStepHeaderProps = getStepData('getStepHeaderProps', formData);
  const renderFooter = getStepData('renderFooter', formData);
  const renderBody = getStepData('renderBody', formData);

  const onAlert = async (message: string, type: 'success' | 'error') => {
    if (type === 'success') {
      handleSuccess({
        message,
      });
    } else {
      handleError({ message });
    }
    onClose();
  };

  const onSuccess = () => {
    onAlert(t('todo_added_to_selected_candidates'), 'success');
  };

  return (
    <MultiStepForm
      apiFunc={batchAddCandidateTodo}
      getValidationSchema={getTodoValidationSchema}
      totalSteps={totalSteps}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      onSuccess={onSuccess}
      onFailure={(error) =>
        onAlert(error.message ?? error.defaultMessage ?? 'Error!', 'error')
      }
      enableReinitialize
      formName="createTodoForm"
      isOpenAnimation
      transform={(values: any) => {
        return transform(values);
      }}
      initialValues={{
        selectedCandidates: [],
      }}
    />
  );
};

export default CreateTodoModal;

function transform(values: any) {
  return {
    ...newCandidateTodoTransform(values),
    ...datesPopperTransformer(values),
    fileIds: values?.fileIds,
    candidateIds: values?.selectedCandidates,
  };
}
