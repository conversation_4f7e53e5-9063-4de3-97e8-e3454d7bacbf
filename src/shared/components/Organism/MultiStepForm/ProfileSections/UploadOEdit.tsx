import Flex from 'shared/uikit/Flex';

import type { Dispatch, SetStateAction } from 'react';
import React from 'react';
import Button from 'shared/uikit/Button';
import { HorizontalDividerWithLabel } from 'shared/components/molecules/HorizontalDividerWithLabel';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import {
  closeMultiStepForm,
  openMultiStepForm,
} from 'shared/hooks/useMultiStepForm';
import { uploadResumeProfile } from 'shared/utils/api/resumeParser';
import useProfilePage from 'shared/hooks/useProfilePage';
import HighlightPattern from 'shared/svg/HighlighPattern';
import useToast from 'shared/uikit/Toast/useToast';
import { getToasterErrorMessage } from 'shared/utils/toolkit/getToasterErrorMessage';
import useGetResume from '@shared/hooks/useGetResume';
import useUploadResumeAndAnalyze from '@shared/hooks/useUploadResumeAndAnalyze';
import ResumeUploadBox from '@shared/uikit/ResumeUploadBox';
import cnj from '@shared/uikit/utils/cnj';
import classes from './UploadOrEdit.module.scss';

export interface Props {
  setStep: Dispatch<SetStateAction<number>>;
}

const UploadOrEdit: React.FC<Props> = ({ setStep }) => {
  const { t } = useTranslation();
  const { objectDetail: user } = useProfilePage();
  const toast = useToast();
  const { refetch, data } = useGetResume({ userId: user?.id });

  const { uploadResumeAndAnalyze, isUploading, progress, cancelUpload } =
    useUploadResumeAndAnalyze();

  const { mutate: analyzeResume } = useReactMutation({
    apiFunc: uploadResumeProfile,
    onSettled: refetch,
  });

  const onError = (res: any) => {
    const { title, message } = getToasterErrorMessage(res, t);
    toast({
      type: 'error',
      icon: 'times-circle',
      message,
      title,
    });
  };

  const onFilePickedHandler = (originalFile: Blob) => {
    uploadResumeAndAnalyze(originalFile, {
      onSuccess: (res) => {
        closeMultiStepForm('editProfileSections');
        setTimeout(
          () =>
            openMultiStepForm({ formName: 'resumeUpload', data: res?.data }),
          100
        );
      },
      onError,
    });
    analyzeResume({
      file: originalFile,
    });
  };

  return (
    <Flex className={classes.wrapper}>
      <Button
        onClick={() => setStep((prev) => prev + 1)}
        schema="gray"
        label={t('edit_sections')}
        leftIcon="pen-light"
        leftType="fal"
        className={classes.editSectionsButton}
      >
        <HighlightPattern className={classes.patternImage} stroke="#808183" />
      </Button>
      <HorizontalDividerWithLabel label={t('or_fill_profile_with_resume')} />
      <ResumeUploadBox
        value={data}
        onFilePickedHandler={onFilePickedHandler}
        labels={{
          uploadDate: t('upload_date'),
          uploading: t('file_uploading_3dot'),
          uploadNew: t('upload_new_resume'),
        }}
        isAjaxCall
        onChange={() => {}}
        isUploading={isUploading}
        onSuccessDelete={refetch}
        className={cnj('p-16_20', classes.uploadContainer)}
        classNames={{
          info: classes.info,
          icon: 'mt-auto',
        }}
        ownerName={user?.fullName}
        visibleShare={false}
      />
    </Flex>
  );
};

export default UploadOrEdit;
