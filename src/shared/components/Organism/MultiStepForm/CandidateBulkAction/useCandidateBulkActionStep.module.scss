@import '/src/shared/theme/theme.scss';

@layer organism {
  .wrapper {
    flex: 1;
  }

  .richText {
    // flex: 1;
    height: 100%;
    max-height: 100% !important;
    border-radius: 4px !important;

    :global {
      & .ql-blank::before {
        color: colors(secondaryDisabledText) !important;
      }
    }
  }

  .messageWrapper {
    background-color: colors(darkSecondary_hover);
    border: 1px solid colors(techGray_20);
    padding: variables(gutter) * 0.5 variables(gutter);
  }

  .grow {
    flex: 1;
    margin-top: variables(gutter);
  }

  .logo {
    margin-top: 0;
    & svg {
      height: 30px;
    }
  }

  .noPadding {
    padding: 0;
  }
  .infoWrapper {
    border-radius: 4px;
  }
  @media (min-width: breakpoints(tablet)) {
    .grow {
      margin-top: variables(largeGutter);
    }
  }
}
