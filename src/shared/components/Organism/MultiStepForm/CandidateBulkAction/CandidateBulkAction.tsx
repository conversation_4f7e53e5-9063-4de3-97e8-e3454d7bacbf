'use client';

import React, { useState } from 'react';
import { useNoteBulkAction } from '@shared/components/Organism/BulkActions/hooks/useNoteBulkAction';
import { useTodoBulkAction } from '@shared/components/Organism/BulkActions/hooks/useTodoBulkAction';
import { useCandidateBulkActionsStepFour } from '@shared/components/Organism/MultiStepForm/CandidateBulkAction/useCandidateBulkActionsStepFour';
import { useCandidateBulkActionsStepThree } from '@shared/components/Organism/MultiStepForm/CandidateBulkAction/useCandidateBulkActionsStepThree';
import { useCandidateBulkActionsStepTwo } from '@shared/components/Organism/MultiStepForm/CandidateBulkAction/useCandidateBulkActionsStepTwo';
import { useGetApiPartials } from '@shared/components/Organism/MultiStepForm/CandidateBulkAction/utils';
import {
  CANDIDATE_NOTE_MAX_LENGTH,
  DESCRIPTION_MAX_LENGTH,
} from '@shared/constants/enums';
import useTranslation from '@shared/utils/hooks/useTranslation';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import MultiStepForm from '../MultiStepForm';
import { useCandidateBulkActionStepOne } from './useCandidateBulkActionStepOne';
import type { MultiStepFormProps } from '../MultiStepForm';

export type Method = 'google' | 'emails' | 'bulk' | undefined;

const CandidateBulkAction: React.FC = () => {
  const { t } = useTranslation();
  const { data: candidateBulkAction } = useMultiStepFormState(
    'candidateBulkAction'
  );
  const apiPartials = useGetApiPartials();

  const initialStep = candidateBulkAction?.step === '1' ? 0 : 1;
  const [currentStep, setCurrentStep] = useState(1);

  const step1 = useCandidateBulkActionStepOne({ setCurrentStep });
  const step2 = useCandidateBulkActionsStepTwo();
  const step3 = useCandidateBulkActionsStepThree();
  const step4 = useCandidateBulkActionsStepFour();
  const step5 = useNoteBulkAction({
    backStep: 0,
    cancelLabel: t('discard'),
    submitLabel: t('save'),
    descriptionMaxLength: CANDIDATE_NOTE_MAX_LENGTH,
    headerTitle: t('note'),
    stepKey: '3',
  });
  const step6 = useTodoBulkAction({
    backStep: 0,
    cancelLabel: t('discard'),
    descriptionMaxLength: DESCRIPTION_MAX_LENGTH,
    headerTitle: t('todo'),
    stepKey: '4',
    submitLabel: t('save'),
  });

  const steps = [...step1, ...step2, ...step3, ...step4, ...step5, ...step6];

  const onClose = () => closeMultiStepForm('candidateBulkAction');

  const getHeaderProps = getStepData('getHeaderProps', steps);
  const getStepHeaderProps = getStepData('getStepHeaderProps', steps);
  const renderBody = getStepData('renderBody', steps);
  const renderFooter = getStepData('renderFooter', steps);

  return (
    <MultiStepForm
      wide
      initialStep={initialStep}
      apiFunc={apiPartials?.[currentStep]?.apiFunc}
      onSuccess={apiPartials?.[currentStep]?.onSuccess}
      initialValues={apiPartials?.[currentStep]?.initialValues}
      transform={apiPartials?.[currentStep]?.transform}
      showConfirm={false}
      enableReinitialize
      totalSteps={steps?.length}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      isOpenAnimation
      formName="candidateBulkAction"
    />
  );
};

export default CandidateBulkAction;

function getStepData<T extends keyof MultiStepFormProps>(
  key: T,
  data: MultiStepFormProps[]
): MultiStepFormProps[T] {
  return ({ step, ...rest }: any) => data?.[step]?.[key]?.({ step, ...rest });
}
