import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import useResponseToast from '@shared/hooks/useResponseToast';
import {
  updateBulkEmail,
  updateBulkNote,
  updateBulkTodo,
} from '@shared/utils/api/candidates';
import useTranslation from '@shared/utils/hooks/useTranslation';

export const useGetApiPartials = () => {
  const { t } = useTranslation();
  const { handleSuccess } = useResponseToast();
  const { data: candidateBulkAction } = useMultiStepFormState(
    'candidateBulkAction'
  );

  const apiPartials: Record<number, any> = {
    2: {
      apiFunc: updateBulkEmail,
      onSuccess: () => {
        handleSuccess({
          message: t('candidate_bulk_email_message'),
          title: t('candidate_bulk_email_title'),
        });
      },
      transform: (formValues: any) => ({
        templateId: formValues?.template?.value,
        candidateIds: (candidateBulkAction?.candidateIds || []).map((id: any) =>
          Number(id)
        ),
      }),
      initialValues: {
        template: '',
        subject: '',
        message: '',
        attachmentFileIds: null,
        hasFollowUp: false,
        followupPeriod: '',
        followupTitle: '',
        followupMessage: '',
        followupFileIds: null,
      },
    },
    3: {
      apiFunc: updateBulkEmail,
      onSuccess: () => {
        handleSuccess({
          message: t('candidate_bulk_messages_message'),
          title: t('candidate_bulk_messages_title'),
        });
      },
    },
    4: {
      apiFunc: updateBulkNote,
      onSuccess: () => {
        handleSuccess({
          message: t('candidate_bulk_note_message'),
          title: t('candidate_bulk_note_title'),
        });
      },
      transform: (formValues: any) => ({
        body: formValues?.body,
        visibility: formValues?.visibility || 'EVERYONE',
        fileIds: formValues?.fileIds?.map((item: any) => item?.id),
        candidateIds: (candidateBulkAction?.candidateIds || []).map((id: any) =>
          Number(id)
        ),
      }),
      initialValues: {
        body: '',
        visibility: '',
        fileIds: null,
      },
    },
    5: {
      apiFunc: updateBulkTodo,
      onSuccess: () => {
        handleSuccess({
          message: t('candidate_bulk_todo_message'),
          title: t('candidate_bulk_todo_title'),
        });
      },
      transform: (formValues: any) => {
        const [startDate] = formValues.startDate?.split('T') ?? [];
        const startTime = formValues.startTime?.value || '00:00';
        const [endDate] = formValues.endDate?.split('T') ?? [];
        const endTime = formValues.endTime?.value || '00:00';

        return {
          title: formValues?.title,
          description: formValues?.description,
          assigneeUserId: formValues?.assigneeUserId?.value,
          allTeamMembersTagged: formValues?.allTeamMembersTagged,
          start: startDate ? `${startDate}T${startTime}` : '',
          end: endDate ? `${endDate}T${endTime}` : '',
          remind: formValues?.remind,
          fileIds: formValues?.fileIds?.map((item: any) => item?.id),
          candidateIds: (candidateBulkAction?.candidateIds || []).map(
            (id: any) => Number(id)
          ),
        };
      },
      initialValues: {
        fileIds: null,
        title: '',
        description: '',
        assigneeUserId: '',
        // taggedUserIds: [],
        start: '',
        end: '',
        remind: true,
      },
    },
  };

  return apiPartials;
};
