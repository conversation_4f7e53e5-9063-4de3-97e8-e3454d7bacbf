import { useState } from 'react';
import { useTemplateBulkAction } from '@shared/components/Organism/BulkActions/hooks/useTemplateBulkAction';
import { CANDIDATE_NOTE_MAX_LENGTH } from '@shared/constants/enums';
import Icon from '@shared/uikit/Icon';
import Tooltip from '@shared/uikit/Tooltip';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { MultiStepFormProps } from '../MultiStepForm';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

export function useCandidateBulkActionsStepThree(): SingleDataItem[] {
  const { t } = useTranslation();

  const [formData, setFormData] = useState<{
    item: any;
    subject: string;
    message: string;
    fieIds: number[];
    followupFileIds: number[];
    followupMessage: string;
    followupPeriod: string;
    followupTitle: string;
    hasFollowup: boolean;
  } | null>(null);

  const followupPeriodItems = [
    {
      label: translateReplacer(t('n_days'), ['3']),
      value: '_3_DAYS',
    },
    {
      label: t('1_week'),
      value: '_1_WEEK',
    },
    {
      label: translateReplacer(t('n_weeks'), ['2']),
      value: '_2_WEEK',
    },
    {
      label: translateReplacer(t('n_weeks'), ['3']),
      value: '_3_WEEK',
    },
    {
      label: t('1_month'),
      value: '_1_month',
    },
  ];

  const emailStep = useTemplateBulkAction({
    cancelLabel: t('discard'),
    headerLabel: t('email'),
    item: formData?.item,
    onChange: (item) => {
      setFormData({
        item: { value: item.id, label: item.title },
        subject: item?.subject,
        message: item?.message,
        fieIds: item?.fileIds,
        followupFileIds: item?.followupFileIds,
        followupMessage: item?.followupMessage,
        followupPeriod: item?.followupMessage,
        followupTitle: item?.followupTitle,
        hasFollowup: item?.hasFollowup,
      });
    },
    stepKey: '2',
    submitLabel: t('send'),
    backStep: 0,
    additionalFields: [
      {
        name: 'subject',
        cp: 'input',
        label: t('subject'),
        required: false,
        disabled: true,
        readOnly: true,
        value: formData?.subject,
        visibleOptionalLabel: false,
        disabledReadOnly: true,
      },
      {
        name: 'message',
        cp: 'richtext',
        label: t(`message`),
        showEmoji: false,
        className: '!h-[252px] overflow-auto',
        helperText: t('dynamic_template_helper_text'),
        disabled: true,
        required: false,
        readonly: true,
        visibleOptionalLabel: false,
        value: formData?.message,
        defaultValue: formData?.message,
        maxLength: CANDIDATE_NOTE_MAX_LENGTH,
        disabledReadOnly: true,
      },
      {
        name: 'attachmentFileIds',
        cp: 'attachmentPicker',
        value: formData?.fieIds,
        wrapStyle: 'pointer-events-none',
        label: t('attachment'),
        required: false,
        visibleOptionalLabel: false,
        disabledReadOnly: true,
      },
      {
        name: 'hasFollowUp',
        cp: 'checkBox',
        rightComponent: (
          <Tooltip
            placement="top"
            trigger={
              <Icon
                color="secondaryDisabledText"
                type="fal"
                name="info-circle"
                size={15}
              />
            }
          >
            {t('send_a_follow-up')}
          </Tooltip>
        ),
        label: t('has_followup_message'),
        hint: t('followup_checkbox_hint'),
        wrapStyle: 'responsive-margin-top',
        disabled: true,

        visibleOptionalLabel: false,
        value: formData?.hasFollowup,
      },
      {
        name: 'followupPeriod',
        cp: 'dropdownSelect',
        label: t('follow_up_after'),
        wrapStyle: 'responsive-margin-top',
        visibleOptionalLabel: false,
        disabledReadOnly: true,

        options: followupPeriodItems,
        disabled: true,
        value: formData?.followupPeriod,
      },
      {
        name: 'followupTitle',
        cp: 'richtext',
        label: t('message_title'),
        wrapStyle: 'responsive-margin-top',
        singleLine: true,
        showEmoji: false,
        disabled: true,
        value: formData?.followupTitle,
        visibleOptionalLabel: false,
        disabledReadOnly: true,
      },
      {
        name: 'followupMessage',
        cp: 'richtext',
        variant: 'form-input',
        label: t('message'),
        wrapStyle: 'responsive-margin-top',
        magicUrl: true,
        disabled: true,
        helperText: t('dynamic_template_helper_text'),
        value: formData?.followupMessage,
        visibleOptionalLabel: false,
        showEmoji: false,
        disabledReadOnly: true,
        required: false,
        readonly: true,
        className: '!h-[252px] overflow-auto',
      },
      {
        name: 'followupFileIds',
        cp: 'attachmentPicker',
        value: formData?.followupFileIds,
        wrapStyle: 'pointer-events-none',
        label: t('attachment'),
        required: false,
        visibleOptionalLabel: false,
        disabledReadOnly: true,
      },
    ],
  });

  return emailStep;
}
