import dayjs from 'dayjs';
import React, { useMemo } from 'react';
import Text from '@shared/components/molecules/Text/Text';
import Badge from '@shared/uikit/Badge';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import dateFromNow from '@shared/utils/toolkit/dateFromNow';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { type BillingCardProps } from '../types';
import classes from './partials.module.scss';

export default function RefundBillingCard({
  data,
  children,
}: BillingCardProps) {
  const { t } = useTranslation();
  const subtotal = useMemo(() => {
    const price = Number(data?.price);
    const seats = Number(data?.numberOfSeats);

    if (!price || !seats) return undefined;

    return `${seats} x ${price} = $${price * seats}`;
  }, [data?.price, data?.numberOfSeats]);

  const cardNumber = useMemo(() => {
    if (!data?.last4digits) return undefined;

    return `**** **** **** ${data?.last4digits}`;
  }, [data?.last4digits]);

  return (
    <Flex className={classes.mainBillingCardcardWrapper}>
      <Flex className={classes.detailsCardHeader}>
        <Badge
          text={translateReplacer(t('n_seat_refunded'), [
            String(data?.numberOfSeats),
          ])}
          textProps={{ color: 'success', size: 15, height: 18, font: '500' }}
          startIconProps={{
            name: 'sales-light',
            type: 'fal',
            color: 'success',
            size: 16,
          }}
          background="success_10"
          className={classes.badge}
        />
        <Typography size={14} color="secondaryDisabledText">
          {dateFromNow(data?.paymentDate, false, t)}
        </Typography>
      </Flex>
      <Flex className={classes.bodyWrapper}>
        <Text label={t('invoice_ID')} value={data?.invoiceNumber} hideIfEmpty />
        <Text
          label={t('total_cap')}
          value={subtotal}
          hideIfEmpty
          hint={t('refund_seat_total_hint')}
        />
        <Text
          label={t('payment_date')}
          value={dayjs(data?.paymentDate).format('MMM DD, YYYY')}
          hideIfEmpty
        />
        <Text label={t('card_number')} value={cardNumber} hideIfEmpty />
        <Text
          label={t('name_on_payment_method')}
          value={data?.nameOnCard || '-'}
          hideIfEmpty
        />
      </Flex>
      {children}
    </Flex>
  );
}
