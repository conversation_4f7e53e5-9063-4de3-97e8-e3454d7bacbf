import dayjs from 'dayjs';
import React, { useMemo } from 'react';
import PlanInfoCard from '@shared/components/molecules/PlanCad/PlanCard.infoCard';
import PlanCardPrice from '@shared/components/molecules/PlanCad/PlanCard.price';
import Text from '@shared/components/molecules/Text/Text';
import Flex from '@shared/uikit/Flex';
import useTranslation from '@shared/utils/hooks/useTranslation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { type BillingCardProps } from '../types';
import classes from './partials.module.scss';

export default function MainBillingCard({ data, children }: BillingCardProps) {
  const { t } = useTranslation();
  const subtotal = useMemo(() => {
    const price = Number(data?.price);
    const seats = Number(data?.numberOfSeats);

    if (!price || !seats) return undefined;

    return `${seats} x ${price} = $${price * seats}`;
  }, [data?.price, data?.numberOfSeats]);

  const remainingTime = useMemo(() => {
    if (!data?.remainingDays) return undefined;

    return translateReplacer('remaining_time_formatted', [
      String(data.remainingDays),
      dayjs(data?.endDate).format('MMM DD, YYYY'),
    ]);
  }, [data?.endDate, data?.remainingDays]);

  const cardNumber = useMemo(() => {
    if (!data?.last4digits) return undefined;

    return `**** **** **** ${data?.last4digits}`;
  }, [data?.last4digits]);

  return (
    <Flex className={classes.mainBillingCardcardWrapper}>
      <PlanInfoCard
        Icon={data?.Logo}
        label={data?.label}
        cardProps={{
          subTitle: data?.timeSpan,
          valueProps: { color: 'secondaryDisabledText' },
          className: classes.planInfoCard,
        }}
      >
        <PlanCardPrice
          price={data?.price}
          priceProps={{ size: 16, height: 19, font: '600' }}
          priceUnit={data?.priceUnit}
          priceUnitProps={{
            size: 14,
            height: 16,
            isWordWrap: false,
            isTruncated: false,
          }}
          wrapperClassName={classes.planCardPriceWrapper}
        />
      </PlanInfoCard>
      <Flex className={classes.bodyWrapper}>
        <Text label={t('invoice_ID')} value={data?.invoiceNumber} hideIfEmpty />
        <Text label={t('subtotal')} value={subtotal} hideIfEmpty />
        <Text
          label={t('payment_date')}
          value={dayjs(data?.paymentDate).format('MMM DD, YYYY')}
          hideIfEmpty
        />
        <Text label={t('remained_time')} value={remainingTime} hideIfEmpty />
        <Text label={t('card_number')} value={cardNumber} hideIfEmpty />
        <Text
          label={t('name_on_payment_method')}
          value={data?.nameOnCard || '-'}
          hideIfEmpty
        />
      </Flex>
      {children}
    </Flex>
  );
}
