import dayjs from 'dayjs';
import React, { useMemo } from 'react';
import Text from '@shared/components/molecules/Text/Text';
import Badge from '@shared/uikit/Badge';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import dateFromNow from '@shared/utils/toolkit/dateFromNow';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { type BillingCardProps } from '../types';
import classes from './partials.module.scss';

export default function AddBillingCard({ data, children }: BillingCardProps) {
  const { t } = useTranslation();
  const subtotal = useMemo(() => {
    const price = Number(data?.price);
    const seats = Number(data?.numberOfSeats);

    if (!price || !seats) return undefined;

    return `${seats} x ${price} = $${price * seats}`;
  }, [data?.price, data?.numberOfSeats]);

  const remainingTime = useMemo(() => {
    if (!data?.remainingDays) return undefined;

    return translateReplacer(t('remaining_time_formatted'), [
      String(data.remainingDays),
      dayjs(data?.endDate).format('MMM DD, YYYY'),
    ]);
  }, [data?.endDate, data?.remainingDays]);

  const cardNumber = useMemo(() => {
    if (!data?.last4digits) return undefined;

    return `**** **** **** ${data?.last4digits}`;
  }, [data?.last4digits]);

  return (
    <Flex className={classes.mainBillingCardcardWrapper}>
      <Flex className={classes.detailsCardHeader}>
        <Badge
          text={translateReplacer(t('n_seats_added'), [
            String(data?.numberOfSeats),
          ])}
          textProps={{ color: 'brand', size: 15, height: 18, font: '500' }}
          startIconProps={{
            name: 'check',
            color: 'brand',
            size: 16,
          }}
          background="brand_10"
          className={classes.badge}
        />
        <Typography size={14} color="secondaryDisabledText">
          {dateFromNow(data?.paymentDate, false, t)}
        </Typography>
      </Flex>
      <Flex className={classes.bodyWrapper}>
        <Text label={t('invoice_ID')} value={data?.invoiceNumber} hideIfEmpty />
        <Text
          label={t('subtotal')}
          value={subtotal}
          hideIfEmpty
          hint={t('add_seat_subtotal_hint')}
        />
        <Text
          label={t('payment_date')}
          value={dayjs(data?.paymentDate).format('MMM DD, YYYY')}
          hideIfEmpty
        />
        <Text label={t('remained_time')} value={remainingTime} hideIfEmpty />
        <Text label={t('card_number')} value={cardNumber} hideIfEmpty />
        <Text
          label={t('name_on_payment_method')}
          value={data?.nameOnCard || '-'}
          hideIfEmpty
        />
      </Flex>
      {children}
    </Flex>
  );
}
