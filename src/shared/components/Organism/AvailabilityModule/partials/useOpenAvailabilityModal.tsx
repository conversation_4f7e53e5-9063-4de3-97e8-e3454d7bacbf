import type { ScheduleUrlState } from '@shared/types/schedules/schedules';
import { routeNames } from '@shared/utils/constants';
import useHistory from '@shared/utils/hooks/useHistory';
import useLocation from '@shared/utils/hooks/useLocation';
import { useSearchParams } from 'next/navigation';
import { useCallback } from 'react';

const defaultSearchParams: ScheduleUrlState = {
  scheduleAvailabilityPanelData: {
    isInAvailabilities: true,
  },
};

export const useOpenAvailabilityModal = (noRedirection = false) => {
  const { pathname } = useLocation();
  const history = useHistory();
  const searchParams = useSearchParams();
  const hasQueryParams = searchParams.toString() !== '';

  const basePathname = hasQueryParams
    ? `${pathname}${window.location.search}`
    : pathname;
  const queryParamSign = hasQueryParams ? '&' : '?';

  const openFn = useCallback(
    (searchParams = defaultSearchParams) => {
      const baseUrl =
        noRedirection || pathname.includes(routeNames.schedules)
          ? basePathname
          : routeNames.schedulesCalendarMonth;

      const newSearchParams = new URLSearchParams();
      newSearchParams.set('state', JSON.stringify(searchParams));

      history.push(`${baseUrl}${queryParamSign}${newSearchParams.toString()}`);
    },
    [history, pathname, noRedirection]
  );

  const openAvailabilityModal = openFn;

  const openAvailabilityCrEditModal = () => {
    openFn({
      scheduleAvailabilityPanelData: {
        isInAvailabilities: true,
        isInCrEdit: true,
      },
    });
  };

  return { openAvailabilityModal, openAvailabilityCrEditModal, openFn };
};
