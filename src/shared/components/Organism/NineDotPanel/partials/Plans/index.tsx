import React, { useMemo } from 'react';
import PlansPage from '@shared/components/Organism/Plans';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import Typography from '@shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './index.module.scss';
import type { NineDotsMainStepsType } from '@shared/components/Organism/NineDotPanel/types';
import type { PageAccessibilityType } from '@shared/types/page';

interface Props {
  data: PageAccessibilityType[];
  onClose?: (e: any) => void;
  handleNext?: (step?: NineDotsMainStepsType) => void;
  isLoadingAccess: boolean;
}

const Plans = ({ data, onClose, handleNext, isLoadingAccess }: Props) => {
  const { businessPage } = useGetAppObject();
  const { t } = useTranslation();
  const businessPageId = businessPage?.id;
  const hasAccess = useMemo(() => {
    if (isLoadingAccess) return false;
    if (!businessPageId) return false;
    const currentPageMembership = data?.find(
      (el) => el.id === businessPageId
    )?.pageMemberships;

    return currentPageMembership?.some(
      ({ role, status }) =>
        status === 'ACCEPTED' && ['ADMIN', 'OWNER'].includes(role)
    );
  }, [businessPageId, data, isLoadingAccess]);

  if (isLoadingAccess) return <Skeleton className="!h-[200px] !w-full" />;
  if (hasAccess) return <PlansPage onClose={onClose} handleNext={handleNext} />;

  return (
    <Flex className={classes.noPermissionContainer}>
      <Typography align="center" size={16} font="700">
        {t('youre_not_admin_or_owner')}
      </Typography>
      <Typography align="center" size={15} font="400">
        {t('for_premium_contact_admin')}
      </Typography>
    </Flex>
  );
};

export default Plans;
