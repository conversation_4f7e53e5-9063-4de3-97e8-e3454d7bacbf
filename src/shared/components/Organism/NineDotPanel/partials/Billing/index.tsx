import React, { type MouseEvent } from 'react';
import BilingList from '@shared/components/Organism/Billing/BilingList';
import BillingDetails from '@shared/components/Organism/Billing/BillingDetails';
import { type Billing } from '@shared/components/Organism/Billing/types';
import {
  selectData,
  setNineDotPanelState,
  useNineDotPanelState,
} from '@shared/stores/nineDotPanelStore';
import Skeleton from '@shared/uikit/Skeleton';
import {
  getPlanBillingDetails,
  getPlanBillingList,
} from '@shared/utils/api/page';
import { QueryKeys } from '@shared/utils/constants';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import type { PageAccessibilityType } from '@shared/types/page';

interface Props {
  data: PageAccessibilityType[];
  onClose?: (e: any) => void;
}

interface BillingPageDataType {
  selectedBilling?: string;
}

const BillingModal = ({ data, onClose }: Props) => {
  const { selectedBilling } =
    useNineDotPanelState<BillingPageDataType>(selectData);

  const {
    data: billingData,
    isLoading: isLoadingBillingData,
    isFetching: isFetchingBillingData,
  } = useReactInfiniteQuery<Billing>([QueryKeys.getBillingList], {
    func: getPlanBillingList,
    size: 10,
  });

  const { data: billingDetailsData, isLoading: isLoadingBillingDetails } =
    useReactQuery({
      action: {
        key: [QueryKeys.getBillingDetails, selectedBilling],
        apiFunc: getPlanBillingDetails,
        params: { id: selectedBilling },
        spreadParams: true,
      },
      config: {
        enabled: !!selectedBilling,
      },
    });

  const onSelectBilling = (billing: Billing) => (event?: MouseEvent<any>) => {
    console.log({ billing, event });
    setNineDotPanelState({ data: { selectedBilling: billing?.id } });
  };

  const onBack = (event?: MouseEvent<any> | null) => {
    setNineDotPanelState({ data: { selectedBilling: undefined } });
  };

  if (isLoadingBillingData || isLoadingBillingDetails)
    return <Skeleton className="!w-full !h-[233px] m-20" />;
  if (selectedBilling)
    return <BillingDetails data={billingDetailsData} onBack={onBack} />;

  return (
    <BilingList
      data={billingData}
      isLoading={isLoadingBillingData}
      isFetching={isFetchingBillingData}
      onSelect={onSelectBilling}
    />
  );
};

export default BillingModal;
