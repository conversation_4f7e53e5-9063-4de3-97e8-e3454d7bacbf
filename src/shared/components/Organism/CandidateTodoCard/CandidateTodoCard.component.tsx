import { useCallback, useMemo, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import type {
  CandidateTodoRequest,
  ICandidateTodo,
} from '@shared/types/candidates';
import cnj from '@shared/uikit/utils/cnj';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import {
  editCandidateTodo,
  removeCandidateTodo,
} from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import preventClickHandler from '@shared/utils/toolkit/preventClickHandler';
import TodoItem from '@shared/components/molecules/TodoItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import IconButton from '@shared/uikit/Button/IconButton';
import PopperItem from '@shared/uikit/PopperItem';
import FormCard from '@shared/components/molecules/FormCard';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import formValidator from '@shared/utils/form/formValidator';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import Flex from '@shared/uikit/Flex';
import TodoStatus from '@shared/components/molecules/TodoItem/partials/TodoStatus';
import { type TodoStatusType } from '@shared/types/todo';
import {
  candidateEndpoints,
  jobsEndpoints,
} from '@shared/utils/constants/servicesEndpoints';
import request from '@shared/utils/toolkit/request';
import { useTodoFields } from './hooks/useTodoFields';
import classes from './CandidateTodoCard.module.scss';
import { useManagerContext } from '../CandidateManager/CandidateManager.context';

interface CandidateTodoCardProps {
  item: ICandidateTodo;
  candidateId: string;
  className?: string;
}

export default function CandidateTodoCard({
  item,
  candidateId,
  className,
}: CandidateTodoCardProps) {
  const [editMode, setEditMode] = useState(false);
  const { t } = useTranslation();
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
    styles: {
      wrapper: '!w-[520px]',
    },
  });
  const { selectedSummary } = useManagerContext();
  const isCandidateMode = selectedSummary?.type === 'ORIGINAL_CANDIDATE';
  const queryClient = useQueryClient();

  const onSuccessHandler = () => {
    setEditMode(false);
    queryClient.invalidateQueries({
      exact: false,
      queryKey: [QueryKeys.candidateTodos, candidateId],
    });
  };

  const validationSchema = useMemo(getTodoValidationSchema, []);

  const editApiFunc = useCallback(
    async (data: CandidateTodoRequest) =>
      editCandidateTodo(item.id, data, isCandidateMode),
    [item.id]
  );

  const deleteApiFunc = useCallback(
    async () => removeCandidateTodo({ todoId: item.id, isCandidateMode }),
    [item.id]
  );

  const { mutate, isLoading } = useReactMutation({
    apiFunc: async (params: { status: TodoStatusType; id: string }) => {
      const { data } = await request.put(
        isCandidateMode
          ? candidateEndpoints.setTodoStatus(params)
          : jobsEndpoints.setTodoStatus(params)
      );
      return data;
    },
    onSuccess: onSuccessHandler,
  });

  const deleteWithConfirm = () => {
    openConfirmDialog({
      title: t('delete'),
      message: t('delete_item_confirmation'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func: deleteApiFunc,
        onSuccess: onSuccessHandler,
      },
    });
  };

  const menu = [
    {
      id: 'edit',
      icon: 'edit',
      title: t('edit'),
      onClick: (e: React.MouseEvent<HTMLElement>) => {
        preventClickHandler(e);
        setEditMode(true);
      },
    },
    {
      id: 'delete',
      icon: 'trash',
      title: t('delete'),
      onClick: deleteWithConfirm,
    },
  ] as const;

  return !editMode ? (
    <TodoItem
      item={item}
      displayCreator
      cardWrapperProps={{
        classNames: { root: cnj(classes.card, className) },
      }}
      action={
        <Flex flexDir="row" className="justify-between align-center">
          <TodoStatus
            disabled={isLoading}
            value={item.status}
            onChange={(_status) => {
              if (item?.id && _status?.value)
                mutate({ id: item.id, status: _status.value });
            }}
          />
          <PopperMenu
            placement="bottom-end"
            closeOnScroll
            buttonComponent={
              <IconButton type="far" name="ellipsis-h" size="md" />
            }
          >
            {menu?.map((menuItem) => (
              <PopperItem
                onClick={menuItem.onClick}
                label={menuItem.title}
                key={menuItem.id}
                iconName={menuItem.icon}
                iconType="far"
                iconSize={18}
                iconClassName={{ wrapper: '!m-0' }}
              />
            ))}
          </PopperMenu>
        </Flex>
      }
    />
  ) : (
    <FormCard
      initialValues={item}
      onSecondaryClick={() => setEditMode(false)}
      onSuccess={onSuccessHandler}
      validationSchema={validationSchema}
      transform={transformCandidateTodo}
      apiFunc={editApiFunc}
    >
      <CandidateTodoFormBody />
    </FormCard>
  );
}

function CandidateTodoFormBody() {
  const fields = useTodoFields();
  const groups = useMemo(
    () => [
      fields.TITLE,
      fields.DESCRIPTION,
      fields.ASSIGNEE,
      fields.START_DATE,
      fields.START_TIME,
      fields.END_DATE,
      fields.END_TIME,
      fields.ATTACHMENT,
    ],
    [fields]
  );
  return <DynamicFormBuilder className={classes.form} groups={groups} />;
}

function transformCandidateTodo(todo: ICandidateTodo): CandidateTodoRequest {
  const [startDate] = (todo.startDate ?? todo.start)?.split('T') ?? [];
  const startTime = todo.startTime?.value;

  const [endDate] = (todo.endDate ?? todo.end)?.split('T') ?? [];
  const endTime = todo.endTime?.value;
  return {
    title: todo.title,
    description: todo.description,
    start: startDate ? `${startDate}T${startTime}:00.000` : todo.start,
    end: endDate ? `${endDate}T${endTime}:00.000` : todo.end,
    assigneeUserId: +todo.assigneeUser.id,
    remind: todo.remind,
    status: todo.status.value,
  };
}

export const getTodoValidationSchema = () =>
  formValidator.object().shape({
    title: formValidator.string().required(),
    description: formValidator.string().required(),
    assigneeUser: formValidator.object().required(),
  });
