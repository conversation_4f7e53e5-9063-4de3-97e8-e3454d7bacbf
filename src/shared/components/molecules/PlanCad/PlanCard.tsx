import React, { type MouseEvent } from 'react';
import Badge from '@shared/uikit/Badge';
import Button, { type ButtonProps } from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { type Plan } from '@shared/utils/normalizers/plansNormalizer';
import PlanFeaturesList from './PlanCard.features';
import PlanInfoCard from './PlanCard.infoCard';
import classes from './PlanCard.module.scss';
import PlanCardPrice from './PlanCard.price';

export default function PlanCard({
  title,
  Logo,
  price,
  priceUnit,
  features,
  color,
  onSelect,
  onSeeMore,
  buttonProps = {},
  isActive,
}: Plan & {
  onSeeMore?: (e: MouseEvent<any>) => void;
  buttonProps?: ButtonProps;
}) {
  const { t } = useTranslation();

  return (
    <Flex className={classes.cardWrapper}>
      <Flex className="!flex-row items-center justify-between">
        <PlanInfoCard label={title} Icon={Logo} labelColor={color} />
        {isActive ? (
          <Badge
            background="success_10"
            text={t('active')}
            startIconProps={{
              color: 'success',
              name: 'check',
              size: 16,
            }}
            textProps={{
              color: 'success',
              size: 16,
            }}
            className={classes.activeBadge}
          />
        ) : null}
      </Flex>
      <PlanCardPrice price={price} priceUnit={priceUnit} variant="Horizontal" />
      <PlanFeaturesList
        features={features}
        planName={title}
        action={onSeeMore}
      />
      {onSelect || Object.keys(buttonProps)?.length ? (
        <Button
          label={t('select_plan')}
          schema="primary-blue"
          onClick={onSelect}
          {...buttonProps}
        />
      ) : null}
    </Flex>
  );
}
