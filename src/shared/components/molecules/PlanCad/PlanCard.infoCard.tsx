import React, {
  type ComponentProps,
  type FC,
  type PropsWithChildren,
} from 'react';
import InfoCard from '@shared/components/Organism/Objects/Common/InfoCard';
import { type ColorsKeys } from '@shared/uikit/types';
import useTranslation from '@shared/utils/hooks/useTranslation';
import classes from './PlanCard.info.module.scss';

type PlanInfoCardProps = PropsWithChildren<{
  label: string;
  Icon: FC;
  labelColor?: ColorsKeys;
  cardProps?: Partial<ComponentProps<typeof InfoCard>>;
}>;

export default function PlanInfoCard({
  label,
  Icon,
  labelColor,
  cardProps = {},
  children,
}: PlanInfoCardProps) {
  const { t } = useTranslation();

  return (
    <InfoCard
      title={t(label)}
      titleProps={{
        size: 16,
        height: 20,
        font: 'bold',
        color: labelColor,
      }}
      wrapperClassName={classes.infoCardWrapper}
      textWrapperClassName={classes.infoCardTextWrapper}
      avatar={<Icon />}
      disabledHover
      {...cardProps}
      valueProps={{
        ...(cardProps?.valueProps || {}),
        mt: 2,
      }}
    >
      {children}
    </InfoCard>
  );
}
