import { useState, type FC, type PropsWithChildren } from 'react';
import type { ISuggestObject } from '@shared/types/search';
import DateView from '@shared/uikit/DateView';
import Tooltip from '@shared/uikit/Tooltip';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import CardBadge from '../CardBadge/CardBadge';
import CardWrapper from '../CardItem/CardWrapper';
import ObjectInfoCard from '../ObjectInfoCard';
import classes from './CandidateCard.module.scss';
import CheckBox from '@shared/uikit/CheckBox';
import IconButton from '@shared/uikit/Button/IconButton';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import {
  addSavedCandidate,
  addSavedFilter,
  removeSavedCandidate,
  removeSavedFilter,
} from '@shared/utils/api/candidates';
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  useQueryClient,
} from '@tanstack/react-query';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import { QueryKeys } from '@shared/utils/constants';
import { IJob } from '@shared/types/job';

export interface CandidateListItemProps {
  onClick?: VoidFunction;
  isFocused?: boolean;
  className?: string;
  item: ISuggestObject;
  hasCheckBox?: boolean;
  checkedIds?: string[];
  setCheckedIds?: ({ id: string, checked: boolean }) => void;
  refetch: <TPageData>(
    options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined
  ) => Promise<QueryObserverResult<IJob, unknown>>;
}

const CandidateListItem: FC<PropsWithChildren<CandidateListItemProps>> = (
  props
) => {
  const {
    className,
    onClick,
    isFocused,
    item,
    checkedIds,
    setCheckedIds,
    hasCheckBox,
    refetch,
  } = props;
  const { t } = useTranslation();
  const isSaved = item?.isSaved;
  const [isShowSavedIcon, setIsShowSavedIcon] = useState(false);

  const { mutate: addSavedCandidateMutate } = useReactMutation({
    apiFunc: addSavedCandidate,
    onSuccess: () => {
      refetch();
    },
  });

  const { mutate: removeSavedCandidateMutate } = useReactMutation({
    apiFunc: removeSavedCandidate,
    onSuccess: () => {
      refetch();
    },
  });

  const handleBookmark = () => {
    const id = item?.id;
    if (isSaved) {
      removeSavedCandidateMutate(id);
    } else {
      addSavedCandidateMutate(id);
    }
  };

  return (
    <CardWrapper
      isFocused={isFocused}
      onClick={onClick}
      classNames={{
        root: cnj(classes.no_root, className),
        container: classes.no_container,
      }}
      onMouseEnter={() => setIsShowSavedIcon(true)}
      onMouseLeave={() => setIsShowSavedIcon(false)}
    >
      {(isShowSavedIcon || isFocused) && !hasCheckBox && (
        <Flex className="absolute right-20 top-20 z-10 ">
          <IconButton
            size="sm18"
            name="bookmark"
            type={isSaved ? 'fas' : 'far'}
            variant="rectangle"
            colorSchema="secondary-transparent"
            onClick={handleBookmark}
            iconProps={{
              color: isSaved ? 'brand' : 'smoke_coal',
            }}
          />
        </Flex>
      )}
      {hasCheckBox && (
        <CheckBox
          classNames={{
            root: 'absolute right-26 top-26 z-10',
          }}
          value={checkedIds?.find((item) => props?.item?.id === item)}
          onChange={(checked, e) => {
            e.stopPropagation();
            setCheckedIds?.({ id: item?.id, checked });
          }}
        />
      )}
      <ObjectInfoCard
        {...item}
        avatarProps={{ name: item?.firstText }}
        isSecondTextSmall
      />
      <Flex flexDir="row" className="gap-8 items-center">
        <CardBadge
          value={item.notesCount ?? '0'}
          iconsDetails={{ iconName: 'note' }}
          tooltipProps={{
            children: t('notes'),
          }}
          // onClick={badgeActions?.onApplicantsClick}
        />
        <CardBadge
          value={item.todosCount ?? '0'}
          iconsDetails={{ iconName: 'checklist' }}
          tooltipProps={{
            children: t('todos'),
          }}
          // onClick={badgeActions?.onApplicantsClick}
        />
        <CardBadge
          value={item.meetingsCount ?? '0'}
          iconsDetails={{ iconName: 'meeting' }}
          tooltipProps={{
            children: t('meetings'),
          }}
          // onClick={badgeActions?.onApplicantsClick}
        />
        {!!item.createDateTime && (
          <Flex className="ml-auto">
            <Tooltip
              trigger={
                <DateView
                  color="secondaryDisabledText"
                  size={12}
                  font="400"
                  height={18}
                  value={item.createDateTime}
                />
              }
            >
              <Typography size={14} font="400" height={18} color="tooltipText">
                {t('latest_activity')}
              </Typography>
            </Tooltip>
          </Flex>
        )}
      </Flex>
    </CardWrapper>
  );
};

export default CandidateListItem;
