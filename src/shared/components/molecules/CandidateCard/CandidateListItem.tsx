import { useState, type FC, type PropsWithChildren } from 'react';
import type { ISuggestObject } from '@shared/types/search';
import DateView from '@shared/uikit/DateView';
import Tooltip from '@shared/uikit/Tooltip';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import CardBadge from '../CardBadge/CardBadge';
import CardWrapper from '../CardItem/CardWrapper';
import ObjectInfoCard from '../ObjectInfoCard';
import classes from './CandidateCard.module.scss';
import CheckBox from '@shared/uikit/CheckBox';

export interface CandidateListItemProps {
  onClick?: VoidFunction;
  isFocused?: boolean;
  className?: string;
  item: ISuggestObject;
  hasCheckBox?: boolean;
  checkedIds?: string[];
  setCheckedIds?: ({ id: string, checked: boolean }) => void;
}

const CandidateListItem: FC<PropsWithChildren<CandidateListItemProps>> = (
  props
) => {
  const {
    className,
    onClick,
    isFocused,
    item,
    checkedIds,
    setCheckedIds,
    hasCheckBox,
  } = props;
  const { t } = useTranslation();

  return (
    <CardWrapper
      isFocused={isFocused}
      onClick={onClick}
      classNames={{
        root: cnj(classes.no_root, className),
        container: classes.no_container,
      }}
    >
      {hasCheckBox && (
        <CheckBox
          classNames={{
            root: 'absolute right-26 top-26 z-10',
          }}
          value={checkedIds?.find((item) => props?.item?.id === item)}
          onChange={(checked, e) => {
            e.stopPropagation();
            setCheckedIds?.({ id: item?.id, checked });
          }}
        />
      )}
      <ObjectInfoCard
        {...item}
        avatarProps={{ name: item?.firstText }}
        isSecondTextSmall
      />
      <Flex flexDir="row" className="gap-8 items-center">
        <CardBadge
          value={item.notesCount ?? '0'}
          iconsDetails={{ iconName: 'note' }}
          tooltipProps={{
            children: t('notes'),
          }}
          // onClick={badgeActions?.onApplicantsClick}
        />
        <CardBadge
          value={item.todosCount ?? '0'}
          iconsDetails={{ iconName: 'checklist' }}
          tooltipProps={{
            children: t('todos'),
          }}
          // onClick={badgeActions?.onApplicantsClick}
        />
        <CardBadge
          value={item.meetingsCount ?? '0'}
          iconsDetails={{ iconName: 'meeting' }}
          tooltipProps={{
            children: t('meetings'),
          }}
          // onClick={badgeActions?.onApplicantsClick}
        />
        {!!item.createDateTime && (
          <Flex className="ml-auto">
            <Tooltip
              trigger={
                <DateView
                  color="secondaryDisabledText"
                  size={12}
                  font="400"
                  height={18}
                  value={item.createDateTime}
                />
              }
            >
              <Typography size={14} font="400" height={18} color="tooltipText">
                {t('latest_activity')}
              </Typography>
            </Tooltip>
          </Flex>
        )}
      </Flex>
    </CardWrapper>
  );
};

export default CandidateListItem;
