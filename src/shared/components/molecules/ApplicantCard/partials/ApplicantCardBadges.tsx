import Flex from '@shared/uikit/Flex';
import type { FC } from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { ApplicantBaseType } from '@shared/types/jobsProps';
import DateView from '@shared/uikit/DateView';
import CardBadge from '../../CardBadge/CardBadge';

type ApplicantCardBadgesProps = { onClick?: () => void } & Pick<
  ApplicantBaseType,
  'notesCount' | 'todosCount' | 'meetingsCount' | 'dateTime'
>;

const ApplicantCardBadges: FC<ApplicantCardBadgesProps> = (props) => {
  const { meetingsCount, notesCount, todosCount, dateTime, onClick } = props;
  const { t } = useTranslation();
  return (
    <Flex className="gap-8 !flex-row">
      <Flex className="gap-8 !flex-row" onClick={onClick}>
        <CardBadge
          value={String(notesCount ?? 0)}
          iconsDetails={{ iconName: 'note' }}
          tooltipProps={{
            children: t('notes'),
          }}
        />
        <CardBadge
          value={String(todosCount ?? 0)}
          iconsDetails={{ iconName: 'checklist' }}
          tooltipProps={{
            children: t('checklists'),
          }}
        />
        <CardBadge
          value={String(meetingsCount ?? 0)}
          iconsDetails={{ iconName: 'meeting' }}
          tooltipProps={{
            children: t('meetings'),
          }}
        />
      </Flex>

      {!!dateTime && (
        <DateView
          value={dateTime}
          size={12}
          color="secondaryDisabledText"
          className="ml-auto"
        />
      )}
    </Flex>
  );
};

export default ApplicantCardBadges;
