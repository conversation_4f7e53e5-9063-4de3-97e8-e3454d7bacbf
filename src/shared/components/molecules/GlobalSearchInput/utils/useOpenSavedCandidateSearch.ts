import { useMemo } from 'react';
import { processBusinessFilterGroups } from '@shared/components/Organism/SearchFiltersHeader/processBusinessFilterGroups';
import { processUserFilterGroups } from '@shared/components/Organism/SearchFiltersHeader/processUserFilterGroups';
import { useCandidatesFilterGroups } from '@shared/hooks/searchFilters/useCandidatesFilterGroups';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import useGlobalSearchUtilities from '@shared/hooks/useGlobalSearchUtilities';
import { getAllSavedFilters } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import { getGroupFilters, mergeItemWithFilters } from './savedCandidateFilters';

export const useOpenSavedCandidateSearch = () => {
  const { businessPage } = useGetAppObject();
  const { allParams } = useCustomParams();
  const { currentModule } = useGlobalSearchUtilities();
  const isCandidate = currentModule === 'candidates';

  const groups = useCandidatesFilterGroups();

  const { data: items = [], isLoading } = useReactQuery({
    action: {
      key: [QueryKeys.getAllSavedFilters, businessPage?.id],
      apiFunc: getAllSavedFilters,
    },
    config: {
      enabled: isCandidate,
    },
  });

  const selectedItem = items?.find(
    (val) => val?.id === allParams?.saveFilterId
  );

  const filters = getGroupFilters({ groups, isLoading, isBusinessApp });

  const mergedItem = isCandidate
    ? mergeItemWithFilters(selectedItem, filters)
    : null;

  return mergedItem;
};
