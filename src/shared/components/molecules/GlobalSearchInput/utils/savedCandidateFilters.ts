import { processBusinessFilterGroups } from '@shared/components/Organism/SearchFiltersHeader/processBusinessFilterGroups';
import { processUserFilterGroups } from '@shared/components/Organism/SearchFiltersHeader/processUserFilterGroups';

export function getGroupFilters({
  groups,
  isBusinessApp,
  isLoading,
}: {
  groups: any[];
  isLoading: boolean;
  isBusinessApp: boolean;
}) {
  if (isLoading) return [];

  return isBusinessApp
    ? processBusinessFilterGroups(groups)
    : processUserFilterGroups(groups);
}

export function mergeItemWithFilters(
  item: { [key: string]: any },
  filteredItems: any[]
) {
  const newItems = { ...item };

  filteredItems?.forEach((val) => {
    newItems[val.name] = newItems[val.name] || val.getValue() || val.value;
  });

  return newItems;
}
