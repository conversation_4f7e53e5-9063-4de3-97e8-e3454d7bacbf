import React, { Fragment, useRef, useState } from 'react';
import CandidateSearchTypePicker from '@shared/components/Organism/CandidateSearchTypePicker';
import PortalPicker from '@shared/components/Organism/PortalPicker';
import { useSearchDispatch } from '@shared/contexts/search/search.provider';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import IconButton from '@shared/uikit/Button/IconButton';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { searchFilterQueryParams } from 'shared/constants/search';
import useGlobalSearchUtilities from 'shared/hooks/useGlobalSearchUtilities';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import useTranslation from 'shared/utils/hooks/useTranslation';
import GlobalSearchInputSearchResultLayout from './GLobalSearchInput.searchResult.layout';
import classes from './GlobalsSearchInput.component.module.scss';
import Input from './GlobalsSearchInput.input';
import { useOpenSavedCandidateSearch } from './utils/useOpenSavedCandidateSearch';

export interface Props {
  className?: string;
  onClose: () => void;
  inputRef: any;
}

const placeholderMap = {
  projects: 'search_projects_or_id',
  candidates: 'search_candidate_name_j_t_l_s',
  businessJobs: 'search_job_title_c_l_id',
  companies: 'SEARCH_COMPANIES',
};

const GlobalSearchInput: React.FC<Props> = ({
  onClose,
  inputRef,
  className,
}) => {
  const { t } = useTranslation();
  const textInputRef = useRef<HTMLInputElement>(null);
  const locationInputRef = useRef<HTMLInputElement>(null);
  const { currentModule } = useGlobalSearchUtilities();
  const { authUser } = useGetAppObject();
  const countryCode = authUser?.location?.countryCode;
  const { allParams } = useCustomParams();
  const searchedQuery = decodeURIComponent(allParams.query || '');
  const searchLocation = allParams?.[searchFilterQueryParams.locationTitle];
  const [focusedInput, setFocusedInput] = useState('text');
  const searchDispatch = useSearchDispatch();
  const item = useOpenSavedCandidateSearch();

  const openSaveFilterModal = () =>
    searchDispatch({
      type: 'SET_SAVE_SEARCH_FILTER_MODAL_DATA',
      payload: { isOpen: true, item },
    });

  const textInputPlaceHolder =
    placeholderMap[currentModule as keyof typeof placeholderMap] || 'search';

  const InputWrapper = isBusinessApp ? Flex : Fragment;

  return (
    <Flex
      className={cnj(
        classes.inputWrapper,
        className,
        isBusinessApp && classes.businessInputWrapper
      )}
    >
      <InputWrapper
        {...(isBusinessApp ? { flexDir: 'row', className: '!w-full' } : {})}
      >
        {isBusinessApp && <PortalPicker value={currentModule} />}
        <Input
          inputRef={textInputRef}
          ref={inputRef}
          placeholder={t(textInputPlaceHolder)}
          inputStyle={cnj(
            isBusinessApp && classes.businessInput,
            currentModule === 'candidates' && '!rounded-none',
            currentModule === 'jobs' && classes.leftInputWhenHavingDoubleInputs
          )}
          onClose={onClose}
          defaultValue={searchedQuery}
          name="text"
          focusedInput={focusedInput}
          setFocusedInput={setFocusedInput}
          additionalRightIcon={
            currentModule === 'candidates' ? (
              <IconButton
                name="add-bookmark"
                type="far"
                colorSchema="semi-transparent"
                size="md18"
                onClick={openSaveFilterModal}
                className={cnj('mr-8', classes.searchIcon)}
              />
            ) : undefined
          }
        >
          {(props: any) => <GlobalSearchInputSearchResultLayout {...props} />}
        </Input>
        {currentModule === 'candidates' && <CandidateSearchTypePicker />}
      </InputWrapper>
      {currentModule === 'jobs' && (
        <Input
          inputRef={locationInputRef}
          placeholder={t('location')}
          inputStyle={classes.locationInputStyle}
          defaultValue={searchLocation}
          searchIconProps={{
            name: 'map-marker-alt',
            type: 'far',
          }}
          emptyMessage={
            <Typography
              className={classes.empty}
              color="graphene_60"
              size={16}
              height={20}
            >
              {t('no_result_found')}
            </Typography>
          }
          name="location"
          onClose={onClose}
          focusedInput={focusedInput}
          setFocusedInput={setFocusedInput}
          countryCode={countryCode}
        >
          {(props) => (
            <GlobalSearchInputSearchResultLayout {...props} isLocation />
          )}
        </Input>
      )}
    </Flex>
  );
};

export default GlobalSearchInput;
