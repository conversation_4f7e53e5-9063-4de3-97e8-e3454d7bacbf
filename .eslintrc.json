{"extends": ["airbnb", "airbnb-typescript", "next/core-web-vitals", "plugin:prettier/recommended", "plugin:storybook/recommended", "plugin:cypress/recommended", "plugin:@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint", "prettier", "cypress", "unused-imports", "import"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "env": {"cypress/globals": true}, "rules": {"import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index", "type"], "newlines-between": "never", "alphabetize": {"order": "asc", "caseInsensitive": true}, "pathGroupsExcludedImportTypes": ["builtin"]}], "unused-imports/no-unused-imports": "error", "padding-line-between-statements": ["error", {"blankLine": "always", "prev": "import", "next": "*"}, {"blankLine": "any", "prev": "import", "next": "import"}, {"blankLine": "always", "prev": "*", "next": "return"}], "prettier/prettier": ["error", {"endOfLine": "auto"}], "react/react-in-jsx-scope": "off", "react/jsx-props-no-spreading": "off", "react/no-array-index-key": "off", "jsx-a11y/anchor-is-valid": ["error", {"components": ["Link"], "specialLink": ["hrefLeft", "hrefRight"], "aspects": ["<PERSON><PERSON><PERSON><PERSON>", "preferButton"]}], "@typescript-eslint/no-unused-vars": "off", "jsx-a11y/label-has-associated-control": ["error", {"required": {"some": ["nesting", "id"]}}], "no-nested-ternary": "off", "react/prop-types": "off", "arrow-body-style": ["error", "as-needed"], "react/require-default-props": "off", "no-use-before-define": "off", "global-require": "off", "no-console": "off", "no-underscore-dangle": "off", "no-param-reassign": "off", "react/jsx-filename-extension": ["error", {"extensions": [".js", ".jsx", ".ts", ".tsx"]}], "import/extensions": "off", "import/no-extraneous-dependencies": ["error", {"devDependencies": true}], "@typescript-eslint/no-use-before-define": "off", "@typescript-eslint/consistent-type-imports": "error", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/ban-types": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-var-requires": "off", "import/prefer-default-export": "off", "react/function-component-definition": "off", "@typescript-eslint/lines-between-class-members": "off", "@typescript-eslint/no-throw-literal": "off"}}